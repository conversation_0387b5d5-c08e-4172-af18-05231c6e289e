#!/usr/bin/env python3
"""
System Monitor for Football News Bot
مراقب النظام لبوت أخبار كرة القدم

This script monitors the bot's performance and health.
"""

import os
import time
import sqlite3
from datetime import datetime, timedelta
from utils.logger import logger
import json

class SystemMonitor:
    """Monitor system health and performance"""
    
    def __init__(self):
        self.db_path = 'database/news.db'
        self.log_file = 'logs/bot.log'
    
    def check_database_health(self):
        """Check database health and statistics"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get total articles
            cursor.execute("SELECT COUNT(*) FROM news_articles")
            total_articles = cursor.fetchone()[0]
            
            # Get articles from last 24 hours
            yesterday = datetime.now() - timedelta(days=1)
            cursor.execute("SELECT COUNT(*) FROM news_articles WHERE created_at > ?", 
                         (yesterday.isoformat(),))
            recent_articles = cursor.fetchone()[0]
            
            # Get articles by source
            cursor.execute("SELECT source, COUNT(*) FROM news_articles GROUP BY source")
            source_stats = cursor.fetchall()
            
            conn.close()
            
            return {
                'status': 'healthy',
                'total_articles': total_articles,
                'recent_articles': recent_articles,
                'source_stats': dict(source_stats)
            }
            
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def check_log_health(self):
        """Check log file for errors and warnings"""
        try:
            if not os.path.exists(self.log_file):
                return {
                    'status': 'warning',
                    'message': 'Log file not found'
                }
            
            # Read last 100 lines
            with open(self.log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                recent_lines = lines[-100:] if len(lines) > 100 else lines
            
            # Count errors and warnings
            errors = sum(1 for line in recent_lines if 'ERROR' in line)
            warnings = sum(1 for line in recent_lines if 'WARNING' in line)
            
            return {
                'status': 'healthy' if errors == 0 else 'warning',
                'recent_errors': errors,
                'recent_warnings': warnings,
                'total_recent_logs': len(recent_lines)
            }
            
        except Exception as e:
            logger.error(f"Log health check failed: {e}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def check_file_system(self):
        """Check important files and directories"""
        important_files = [
            'main.py',
            'config.py',
            '.env',
            'requirements.txt'
        ]
        
        important_dirs = [
            'scraper',
            'generator',
            'publisher',
            'utils',
            'database'
        ]
        
        file_status = {}
        for file in important_files:
            file_status[file] = os.path.exists(file)
        
        dir_status = {}
        for directory in important_dirs:
            dir_status[directory] = os.path.exists(directory)
        
        return {
            'files': file_status,
            'directories': dir_status,
            'all_files_ok': all(file_status.values()),
            'all_dirs_ok': all(dir_status.values())
        }
    
    def check_disk_space(self):
        """Check available disk space"""
        try:
            import shutil
            total, used, free = shutil.disk_usage('.')
            
            free_gb = free // (1024**3)
            total_gb = total // (1024**3)
            used_percent = (used / total) * 100
            
            return {
                'free_gb': free_gb,
                'total_gb': total_gb,
                'used_percent': round(used_percent, 2),
                'status': 'healthy' if free_gb > 1 else 'warning'
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def generate_report(self):
        """Generate comprehensive system report"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'database': self.check_database_health(),
            'logs': self.check_log_health(),
            'filesystem': self.check_file_system(),
            'disk_space': self.check_disk_space()
        }
        
        # Overall health status
        statuses = [
            report['database']['status'],
            report['logs']['status'],
            'healthy' if report['filesystem']['all_files_ok'] and report['filesystem']['all_dirs_ok'] else 'warning',
            report['disk_space']['status']
        ]
        
        if 'error' in statuses:
            overall_status = 'error'
        elif 'warning' in statuses:
            overall_status = 'warning'
        else:
            overall_status = 'healthy'
        
        report['overall_status'] = overall_status
        
        return report
    
    def print_report(self, report):
        """Print formatted report"""
        print("🔍 System Health Report")
        print("=" * 50)
        print(f"📅 Time: {report['timestamp']}")
        print(f"🏥 Overall Status: {self.get_status_emoji(report['overall_status'])} {report['overall_status'].upper()}")
        print()
        
        # Database
        db = report['database']
        print(f"🗄️ Database: {self.get_status_emoji(db['status'])}")
        if db['status'] != 'error':
            print(f"   📊 Total Articles: {db['total_articles']}")
            print(f"   📈 Recent Articles (24h): {db['recent_articles']}")
            print(f"   📰 Sources: {', '.join(db['source_stats'].keys())}")
        else:
            print(f"   ❌ Error: {db['error']}")
        print()
        
        # Logs
        logs = report['logs']
        print(f"📝 Logs: {self.get_status_emoji(logs['status'])}")
        if logs['status'] != 'error':
            print(f"   ⚠️ Recent Warnings: {logs.get('recent_warnings', 0)}")
            print(f"   ❌ Recent Errors: {logs.get('recent_errors', 0)}")
        else:
            print(f"   ❌ Error: {logs.get('error', 'Unknown error')}")
        print()
        
        # File System
        fs = report['filesystem']
        print(f"📁 File System: {self.get_status_emoji('healthy' if fs['all_files_ok'] and fs['all_dirs_ok'] else 'warning')}")
        print(f"   📄 Files OK: {sum(fs['files'].values())}/{len(fs['files'])}")
        print(f"   📂 Directories OK: {sum(fs['directories'].values())}/{len(fs['directories'])}")
        print()
        
        # Disk Space
        disk = report['disk_space']
        print(f"💾 Disk Space: {self.get_status_emoji(disk['status'])}")
        if disk['status'] != 'error':
            print(f"   💿 Free Space: {disk['free_gb']} GB")
            print(f"   📊 Used: {disk['used_percent']}%")
        else:
            print(f"   ❌ Error: {disk['error']}")
    
    def get_status_emoji(self, status):
        """Get emoji for status"""
        emojis = {
            'healthy': '✅',
            'warning': '⚠️',
            'error': '❌'
        }
        return emojis.get(status, '❓')
    
    def save_report(self, report, filename=None):
        """Save report to file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"system_report_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"📄 Report saved to: {filename}")
        except Exception as e:
            print(f"❌ Failed to save report: {e}")

def main():
    """Main function"""
    monitor = SystemMonitor()
    
    print("🔍 Generating system health report...")
    report = monitor.generate_report()
    
    monitor.print_report(report)
    
    # Ask if user wants to save report
    try:
        save = input("\n💾 Save report to file? (y/n): ").lower().strip()
        if save in ['y', 'yes']:
            monitor.save_report(report)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")

if __name__ == '__main__':
    main()

#!/usr/bin/env python3
"""
Football News Bot - Easy Run Script
بوت أخبار كرة القدم - سكريبت تشغيل مبسط

This script provides an easy way to run the football news bot with different options.
"""

import sys
import os
import subprocess
import time
from utils.logger import logger

def print_banner():
    """Print a nice banner"""
    banner = """
    ==========================================
           Football News Bot       
    ========================================== 
    """
    print(banner)

def check_requirements():
    """Check if all requirements are installed"""
    try:
        import requests
        import beautifulsoup4
        import selenium
        import google.auth
        import google.generativeai
        import telegram
        import PIL
        import dotenv
        logger.info("All requirements are installed")
        return True
    except ImportError as e:
        logger.error(f"Missing requirement: {e}")
        print(f"Missing requirement: {e}")
        print("Please run: pip install -r requirements.txt")
        return False

def check_env_file():
    """Check if .env file exists"""
    if os.path.exists('.env'):
        logger.info(".env file found")
        return True
    else:
        logger.warning(".env file not found")
        print(".env file not found. Please create it from env.example")
        return False

def run_test_mode():
    """Run the bot in test mode (single cycle)"""
    print("Running in TEST mode (single cycle)...")
    logger.info("Starting bot in test mode")
    
    try:
        result = subprocess.run([sys.executable, 'main.py', '--test'], 
                              capture_output=False, text=True)
        if result.returncode == 0:
            print("Test completed successfully!")
            logger.info("Test mode completed successfully")
        else:
            print("Test failed!")
            logger.error("Test mode failed")
    except Exception as e:
        print(f"Error running test: {e}")
        logger.error(f"Error running test: {e}")

def run_production_mode():
    """Run the bot in production mode (continuous)"""
    print("Running in PRODUCTION mode (continuous)...")
    print("Press Ctrl+C to stop the bot")
    logger.info("Starting bot in production mode")
    
    try:
        subprocess.run([sys.executable, 'main.py'], capture_output=False, text=True)
    except KeyboardInterrupt:
        print("\nBot stopped by user")
        logger.info("Bot stopped by user")
    except Exception as e:
        print(f"Error running bot: {e}")
        logger.error(f"Error running bot: {e}")

def test_blogger_auth():
    """Test and setup Blogger authentication"""
    print("Testing Blogger Authentication...")
    print("-" * 40)

    try:
        # Import auth manager
        from blogger_auth_manager import BloggerAuthManager

        auth_manager = BloggerAuthManager()

        print("1. Testing existing credentials...")
        creds = auth_manager.load_credentials_from_env()
        if creds and creds.valid:
            print("Valid credentials found")
        else:
            print("No valid credentials found")

        print("\n2. Testing Blogger API connection...")
        success, message = auth_manager.test_blogger_connection()
        if success:
            print(f"{message}")
        else:
            print(f"{message}")
            print("\nWould you like to setup authentication? (y/n): ", end="")
            try:
                response = input().lower().strip()
                if response in ['y', 'yes', 'نعم', 'ن']:
                    print("\nStarting authentication setup...")
                    new_creds = auth_manager.get_valid_credentials()
                    if new_creds:
                        print("Authentication setup completed!")
                        # Test again
                        success, message = auth_manager.test_blogger_connection()
                        if success:
                            print(f"{message}")
                        else:
                            print(f"Still having issues: {message}")
                    else:
                        print("Authentication setup failed")
                else:
                    print("Skipping authentication setup")
            except KeyboardInterrupt:
                print("\nSkipping authentication setup")

        if success:
            print("\n3. Testing post creation...")
            post_success, post_message = auth_manager.test_post_creation()
            if post_success:
                print(f"{post_message}")
            else:
                print(f"{post_message}")

    except ImportError:
        print("BloggerAuthManager not available")
        print("Please ensure blogger_auth_manager.py exists")
    except Exception as e:
        print(f"Error testing Blogger auth: {e}")

def run_component_tests():
    """Run individual component tests"""
    print("Running component tests...")

    tests = [
        ("Database", "test_database.py"),
        ("Blogger Publisher", "test_blogger.py"),
        ("Telegram Publisher", "test_telegram.py"),
        ("All Components", "tests/test_all_components.py")
    ]

    for test_name, test_file in tests:
        if os.path.exists(test_file):
            print(f"\nTesting {test_name}...")
            try:
                result = subprocess.run([sys.executable, test_file],
                                      capture_output=True, text=True, timeout=60)
                if result.returncode == 0:
                    print(f"{test_name} test passed")
                else:
                    print(f"{test_name} test failed")
                    if result.stderr:
                        print(f"Error: {result.stderr[:200]}...")
            except subprocess.TimeoutExpired:
                print(f"{test_name} test timed out")
            except Exception as e:
                print(f"Error testing {test_name}: {e}")
        else:
            print(f"{test_name} test file not found: {test_file}")

def show_status():
    """Show system status"""
    print("System Status:")
    print("-" * 40)
    
    # Check files
    files_to_check = [
        (".env", "Environment file"),
        ("requirements.txt", "Requirements file"),
        ("database/news.db", "Database file"),
        ("main.py", "Main script"),
    ]
    
    for file_path, description in files_to_check:
        if os.path.exists(file_path):
            print(f"{description}: Found")
        else:
            print(f"{description}: Missing")
    
    # Check directories
    dirs_to_check = ["scraper", "generator", "publisher", "utils", "database"]
    for dir_path in dirs_to_check:
        if os.path.exists(dir_path):
            print(f"{dir_path}/ directory: Found")
        else:
            print(f"{dir_path}/ directory: Missing")

def main():
    """Main function"""
    print_banner()
    
    # Basic checks
    if not check_requirements():
        return
    
    check_env_file()
    
    while True:
        print("\nChoose an option:")
        print("1. Run Test Mode (single cycle)")
        print("2. Run Production Mode (continuous)")
        print("3. Test Blogger Authentication")
        print("4. Run Component Tests")
        print("5. Show System Status")
        print("6. View System Report")
        print("7. Exit")

        try:
            choice = input("\nEnter your choice (1-7): ").strip()

            if choice == '1':
                run_test_mode()
            elif choice == '2':
                run_production_mode()
            elif choice == '3':
                test_blogger_auth()
            elif choice == '4':
                run_component_tests()
            elif choice == '5':
                show_status()
            elif choice == '6':
                if os.path.exists('SYSTEM_STATUS_REPORT.md'):
                    print("\nOpening system report...")
                    with open('SYSTEM_STATUS_REPORT.md', 'r', encoding='utf-8') as f:
                        print(f.read())
                else:
                    print("System report not found")
            elif choice == '7':
                print("Goodbye!")
                break
            else:
                print("Invalid choice. Please enter 1-7.")
                
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == '__main__':
    main()
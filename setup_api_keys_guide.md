# 🔑 دليل إعداد مفاتيح API للصور الذكية

## 📝 الخطوات المطلوبة

### 1. **إنشاء ملف .env**
```bash
# في مجلد المشروع، انسخ الملف:
copy .env.example .env
```

### 2. **الحصول على Pixabay API Key**

#### أ) زيارة الموقع:
- اذهب إلى: https://pixabay.com/api/docs/

#### ب) إنشاء حساب:
- اضغط "Join" أو "Sign Up"
- أدخل بياناتك (مجاني تماماً)

#### ج) الحصول على المفتاح:
- بعد تسجيل الدخول، اذهب إلى: https://pixabay.com/api/docs/
- ستجد مفتاح API الخاص بك في أعلى الصفحة
- انسخ المفتاح (يبدأ بأرقام وحروف)

### 3. **الحصول على Unsplash Access Key**

#### أ) زيارة الموقع:
- اذهب إلى: https://unsplash.com/developers

#### ب) إنشاء حساب:
- اضغط "Register as a developer"
- أدخل بياناتك

#### ج) إنشاء تطبيق:
- اضغط "New Application"
- اختر "Demo" للاستخدام المجاني
- املأ البيانات:
  - Application name: "Football News Bot"
  - Description: "AI-powered football news image generation"

#### د) الحصول على المفتاح:
- بعد إنشاء التطبيق، انسخ "Access Key"

### 4. **تعديل ملف .env**

افتح ملف `.env` وضع المفاتيح:

```bash
# ===== EXISTING VARIABLES =====
GOOGLE_API_KEY=your_existing_google_key_here

# ===== NEW VARIABLES FOR SMART IMAGES =====
PIXABAY_API_KEY=12345678-abcd1234efgh5678ijkl9012
UNSPLASH_ACCESS_KEY=abcd1234efgh5678ijkl9012mnop3456qrst7890

# ===== IMAGE GENERATION SETTINGS =====
SMART_IMAGES_ENABLED=true
MIN_RELEVANCE_SCORE=6.0
MAX_SEARCH_RESULTS=20
MAX_ANALYZE_IMAGES=5
FALLBACK_IMAGES_ENABLED=true
```

## 🔒 **أمان المفاتيح**

### ⚠️ **مهم جداً:**
- **لا تشارك** ملف `.env` مع أحد
- **لا ترفع** ملف `.env` إلى GitHub أو أي موقع
- **احتفظ بنسخة احتياطية** من المفاتيح في مكان آمن

### ✅ **الحماية:**
- ملف `.env` محمي تلقائياً (لا يظهر في Git)
- المفاتيح تُقرأ فقط عند تشغيل البرنامج
- لا تظهر في اللوجز أو الأخطاء

## 🧪 **اختبار المفاتيح**

بعد إضافة المفاتيح، اختبر النظام:

```bash
python test_smart_images.py
```

### النتيجة المتوقعة:
```
🔑 Testing API Keys...
✅ PIXABAY_API_KEY: Available
✅ UNSPLASH_ACCESS_KEY: Available
✅ GOOGLE_API_KEY: Available

🔍 Testing Image Search...
✅ Pixabay results: 15 images found
✅ Unsplash results: 12 images found
```

## 🚀 **بدء الاستخدام**

بعد إعداد المفاتيح، النظام جاهز:

```bash
# تشغيل الوكيل العادي
python main.py

# أو تشغيل اختبار واحد
python main.py --test
```

## 💡 **نصائح مهمة**

### 1. **حدود الاستخدام المجاني:**
- **Pixabay**: 5000 طلب/ساعة (أكثر من كافي)
- **Unsplash**: 50 طلب/ساعة (مناسب للاستخدام العادي)

### 2. **إذا نفدت الحصة:**
- النظام سيستخدم مصادر أخرى
- أو سينشئ صور احتياطية جميلة
- لن يتوقف عن العمل

### 3. **تحسين الأداء:**
- ضع `MAX_ANALYZE_IMAGES=3` لتوفير استهلاك Gemini
- ضع `MAX_SEARCH_RESULTS=15` لتوفير استهلاك APIs

## ❓ **استكشاف الأخطاء**

### مشكلة: "API key not found"
**الحل:**
- تأكد من وجود ملف `.env` في مجلد المشروع
- تأكد من كتابة اسم المتغير بشكل صحيح
- تأكد من عدم وجود مسافات قبل أو بعد المفتاح

### مشكلة: "Invalid API key"
**الحل:**
- تأكد من نسخ المفتاح كاملاً
- تأكد من أن الحساب مفعل
- جرب إنشاء مفتاح جديد

### مشكلة: "Rate limit exceeded"
**الحل:**
- انتظر ساعة وجرب مرة أخرى
- قلل من `MAX_SEARCH_RESULTS` و `MAX_ANALYZE_IMAGES`

## 📞 **الدعم**

إذا واجهت مشاكل:
1. تأكد من اتباع الخطوات بالترتيب
2. اختبر كل مفتاح على حدة
3. تحقق من رسائل الخطأ في اللوج

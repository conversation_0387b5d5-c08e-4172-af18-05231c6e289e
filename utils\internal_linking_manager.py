import sys
import os
import re

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.gemini_manager import gemini_manager
from utils.logger import logger
from database import database


class InternalLinkingManager:
    """Manages internal linking for football articles."""
    
    def __init__(self):
        # Extended list of common players with variations
        self.common_players = [
            # Arabic names
            'ميسي', 'رونالدو', 'كريستيانو رونالدو', 'ليونيل ميسي', 'مبابي', 'كيليان مبابي',
            'نيمار', 'صلاح', 'محمد صلاح', 'هالاند', 'إيرلينغ هالاند', 'بنزيما', 'كريم بنزيما',
            'مودريتش', 'لوكا مودريتش', 'دي بروين', 'كيفين دي بروين', 'لوكاكو', 'روميلو لوكاكو',
            'فينيسيوس', 'فينيسيوس جونيور', 'بيليجهام', 'جود بيليجهام', 'رودريجو', 'رودريجو جويز',
            'جافي', 'بيدري', 'فيران توريس', 'أنسو فاتي', 'ليفاندوفسكي', 'روبرت ليفاندوفسكي',
            'ماني', 'ساديو ماني', 'فان دايك', 'فيرجيل فان دايك', 'أليسون', 'أليسون بيكر',
            'كين', 'هاري كين', 'راشفورد', 'ماركوس راشفورد', 'سانشو', 'جادون سانشو',
            'غريزمان', 'أنطوان غريزمان', 'فيليكس', 'جواو فيليكس', 'كوكي', 'كوكي ريسوريكشن',
            # Egyptian players
            'طاهر محمد طاهر', 'أكرم توفيق', 'مروان محسن', 'حسين الشحات', 'أحمد فتحي',
            'عبد الله السعيد', 'وليد سليمان', 'محمود كهربا', 'أحمد حجازي', 'محمود تريزيجيه'
        ]

        # Extended list of teams with variations
        self.common_teams = [
            # Spanish teams
            'ريال مدريد', 'الريال', 'النادي الملكي', 'برشلونة', 'البارسا', 'النادي الكتالوني',
            'أتلتيكو مدريد', 'الأتلتي', 'إشبيلية', 'فالنسيا', 'فياريال', 'ريال بيتيس',
            # English teams
            'ليفربول', 'الريدز', 'مانشستر سيتي', 'السيتي', 'مانشستر يونايتد', 'اليونايتد',
            'تشيلسي', 'البلوز', 'أرسنال', 'الجانرز', 'توتنهام', 'السبيرز', 'نيوكاسل',
            # Other European teams
            'باريس سان جيرمان', 'البي إس جي', 'بايرن ميونخ', 'البافاري', 'دورتمند',
            'يوفنتوس', 'اليوفي', 'إنتر ميلان', 'الإنتر', 'ميلان', 'الروسونيري',
            'نابولي', 'روما', 'لاتسيو', 'أياكس', 'بورتو', 'بنفيكا',
            # Egyptian teams
            'الأهلي', 'النادي الأهلي', 'الأحمر', 'الزمالك', 'النادي الأبيض', 'الأبيض',
            'بيراميدز', 'المصري', 'الإسماعيلي', 'الداخلية', 'طلائع الجيش', 'إنبي'
        ]

        # Extended list of competitions
        self.common_competitions = [
            # International competitions
            'دوري أبطال أوروبا', 'دوري الأبطال', 'الشامبيونز ليج', 'الدوري الأوروبي', 'يوروبا ليج',
            'كأس العالم', 'المونديال', 'يورو', 'بطولة أوروبا', 'كأس الأمم الأفريقية', 'الكان',
            'كوبا أمريكا', 'الكونكاكاف', 'دوري الأمم الأوروبية',
            # Domestic leagues
            'الدوري الإنجليزي', 'البريميرليج', 'الدوري الإسباني', 'الليجا', 'لا ليجا',
            'الدوري الإيطالي', 'السيريا أ', 'الدوري الألماني', 'البوندسليجا',
            'الدوري الفرنسي', 'ليج 1', 'الدوري المصري', 'دوري نجوم مصر',
            # Cup competitions
            'كأس الملك', 'كأس إنجلترا', 'كأس إيطاليا', 'كأس ألمانيا', 'كأس فرنسا',
            'كأس مصر', 'كأس السوبر المصري', 'كأس السوبر الأوروبي'
        ]

    def extract_entities_with_ai(self, text):
        """Extract entities (players, teams, competitions) from text using Gemini AI."""
        try:
            model = gemini_manager.get_model()
            
            prompt = f"""
أنت خبير في استخراج الكيانات الرياضية من النصوص العربية. مهمتك استخراج أسماء اللاعبين والأندية والبطولات من النص التالي بدقة عالية.

**النص:**
{text}

**التعليمات الدقيقة:**
1. استخرج فقط الأسماء المذكورة صراحة في النص
2. استخدم الأسماء الشائعة والمعروفة (مثل "ميسي" بدلاً من "ليونيل أندريس ميسي كوتشيتيني")
3. تجاهل الضمائر والإشارات غير المباشرة
4. اكتب الأسماء بالشكل الأكثر شيوعاً في الإعلام العربي

**أمثلة للأسماء المقبولة:**
- اللاعبين: ميسي، رونالدو، صلاح، مبابي، هالاند، بنزيما، نيمار
- الأندية: ريال مدريد، برشلونة، ليفربول، الأهلي، الزمالك، باريس سان جيرمان
- البطولات: دوري أبطال أوروبا، الدوري الإنجليزي، كأس العالم، الدوري المصري

**التنسيق المطلوب (لا تكتب أي شيء آخر):**
PLAYERS: [قائمة أسماء اللاعبين مفصولة بفاصلة، أو "لا يوجد"]
TEAMS: [قائمة أسماء الأندية مفصولة بفاصلة، أو "لا يوجد"]
COMPETITIONS: [قائمة أسماء البطولات مفصولة بفاصلة، أو "لا يوجد"]

**مثال للإخراج:**
PLAYERS: ميسي, رونالدو, صلاح
TEAMS: ريال مدريد, برشلونة
COMPETITIONS: دوري أبطال أوروبا, الدوري الإسباني
            """
            
            response = model.generate_content(prompt)
            entities_text = response.text.strip()
            
            return self._parse_entities_response(entities_text)
            
        except Exception as e:
            logger.error(f"Failed to extract entities with AI: {e}")
            return self._extract_entities_fallback(text)

    def _parse_entities_response(self, response_text):
        """Parse the AI response to extract entities."""
        entities = {'players': [], 'teams': [], 'competitions': []}
        
        try:
            lines = response_text.split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('PLAYERS:'):
                    players_text = line.replace('PLAYERS:', '').strip()
                    if players_text and players_text != 'لا يوجد':
                        entities['players'] = [p.strip() for p in players_text.split(',') if p.strip()]
                
                elif line.startswith('TEAMS:'):
                    teams_text = line.replace('TEAMS:', '').strip()
                    if teams_text and teams_text != 'لا يوجد':
                        entities['teams'] = [t.strip() for t in teams_text.split(',') if t.strip()]
                
                elif line.startswith('COMPETITIONS:'):
                    comps_text = line.replace('COMPETITIONS:', '').strip()
                    if comps_text and comps_text != 'لا يوجد':
                        entities['competitions'] = [c.strip() for c in comps_text.split(',') if c.strip()]
            
            logger.info(f"Extracted entities: {entities}")
            return entities
            
        except Exception as e:
            logger.error(f"Failed to parse entities response: {e}")
            return {'players': [], 'teams': [], 'competitions': []}

    def _extract_entities_fallback(self, text):
        """Fallback method to extract entities using predefined lists."""
        entities = {'players': [], 'teams': [], 'competitions': []}
        
        text_lower = text.lower()
        
        # Extract players
        for player in self.common_players:
            if player.lower() in text_lower:
                entities['players'].append(player)
        
        # Extract teams
        for team in self.common_teams:
            if team.lower() in text_lower:
                entities['teams'].append(team)
        
        # Extract competitions
        for comp in self.common_competitions:
            if comp.lower() in text_lower:
                entities['competitions'].append(comp)
        
        return entities

    def find_related_articles(self, entities):
        """Find related articles for the given entities."""
        related_articles = {}
        
        # Search for articles by players
        for player in entities.get('players', []):
            articles = database.find_articles_by_entity(player, 'player', limit=2)
            if articles:
                related_articles[player] = articles[0]  # Take the most recent one
        
        # Search for articles by teams
        for team in entities.get('teams', []):
            articles = database.find_articles_by_entity(team, 'team', limit=2)
            if articles:
                related_articles[team] = articles[0]  # Take the most recent one
        
        # Search for articles by competitions
        for comp in entities.get('competitions', []):
            articles = database.find_articles_by_entity(comp, 'competition', limit=2)
            if articles:
                related_articles[comp] = articles[0]  # Take the most recent one
        
        return related_articles

    def add_internal_links(self, content, entities):
        """Add internal links to the content based on found entities and related articles."""
        if not entities:
            return content

        related_articles = self.find_related_articles(entities)

        if not related_articles:
            logger.info("No related articles found for internal linking")
            return content

        modified_content = content
        links_added = 0

        # Sort entities by length (longest first) to avoid partial replacements
        sorted_entities = sorted(related_articles.items(), key=lambda x: len(x[0]), reverse=True)

        # Add links for entities that have related articles
        for entity_name, article_info in sorted_entities:
            # Only add link if we haven't added too many (max 3-4 links per article)
            if links_added >= 4:
                break

            # Skip if this entity is already part of a link
            if f'>{entity_name}<' in modified_content:
                continue

            # Create the link HTML
            link_html = f'<a href="{article_info["blog_url"]}" title="{article_info["title"]}">{entity_name}</a>'

            # Replace the first occurrence of the entity name with the link
            # Use word boundaries to avoid partial matches
            pattern = r'\b' + re.escape(entity_name) + r'\b'

            # Check if the entity exists in the content and is not already linked
            if re.search(pattern, modified_content) and f'>{entity_name}<' not in modified_content:
                # Replace only the first occurrence
                modified_content = re.sub(pattern, link_html, modified_content, count=1)
                links_added += 1
                logger.info(f"Added internal link for '{entity_name}' -> {article_info['blog_url']}")

        if links_added > 0:
            logger.info(f"Successfully added {links_added} internal links to the article")

        return modified_content

    def process_article_for_linking(self, title, content):
        """Main method to process an article and add internal links."""
        logger.info(f"Processing article for internal linking: '{title}'")
        
        # Extract entities from both title and content
        full_text = f"{title}\n\n{content}"
        entities = self.extract_entities_with_ai(full_text)
        
        # Add internal links to the content
        linked_content = self.add_internal_links(content, entities)
        
        return linked_content, entities


# Create a global instance
internal_linking_manager = InternalLinkingManager()

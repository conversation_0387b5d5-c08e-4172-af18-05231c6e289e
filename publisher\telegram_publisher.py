
import telegram
import random
from config import TELEGRAM_BOT_TOKEN, TELEGRAM_CHANNEL_ID
from utils.logger import logger

# Import title cleaning function
try:
    from generator.content_generator import clean_article_title
except ImportError:
    def clean_article_title(title):
        return title

# Message templates for variety
MESSAGE_TEMPLATES = [
    {
        "emoji": "🔥⚽",
        "intro": "عاجل من عالم كرة القدم",
        "call_to_action": "اقرأ التفاصيل الكاملة"
    },
    {
        "emoji": "⚡🏆",
        "intro": "خبر ساخن",
        "call_to_action": "شاهد التفاصيل"
    },
    {
        "emoji": "🚨⚽",
        "intro": "آخر الأخبار الرياضية",
        "call_to_action": "اكتشف المزيد"
    },
    {
        "emoji": "🔴⚽",
        "intro": "حصرياً",
        "call_to_action": "تابع القراءة"
    },
    {
        "emoji": "💥🏟️",
        "intro": "من قلب الملاعب",
        "call_to_action": "اقرأ الخبر كاملاً"
    }
]

CLOSING_MESSAGES = [
    "📢 تابعونا للمزيد من الأخبار الحصرية",
    "⚽ نقدم لكم أحدث أخبار كرة القدم",
    "🏆 معكم دائماً في عالم الرياضة",
    "📰 أخبارنا.. شغفكم",
    "⚡ كن أول من يعرف الأخبار"
]

async def send_telegram_notification(title, post_url):
    """Sends a varied, professional notification to the Telegram channel.

    Args:
        title (str): The title of the new post.
        post_url (str): The URL of the new post on Blogger.
    """
    logger.info(f"Sending Telegram notification for: {title}")
    if not TELEGRAM_BOT_TOKEN or not TELEGRAM_CHANNEL_ID:
        logger.warning("Telegram token or channel ID is not set. Skipping notification.")
        return

    try:
        bot = telegram.Bot(token=TELEGRAM_BOT_TOKEN)

        # Clean the title for better presentation
        clean_title = clean_article_title(title)

        # Select random template
        template = random.choice(MESSAGE_TEMPLATES)
        closing = random.choice(CLOSING_MESSAGES)

        # Create varied message
        message = f"""{template['emoji']} **{template['intro']}**

📰 **{clean_title}**

🔗 {template['call_to_action']}: {post_url}

{closing}"""

        await bot.send_message(
            chat_id=TELEGRAM_CHANNEL_ID,
            text=message,
            parse_mode='Markdown',
            disable_web_page_preview=False
        )
        logger.info("Telegram notification sent successfully.")

    except Exception as e:
        logger.error(f"Failed to send Telegram notification: {e}")

if __name__ == '__main__':
    # For testing purposes (requires Telegram token and channel ID in .env)
    import asyncio
    test_title = "اختبار: الأهلي بطلًا للدوري"
    test_url = "https://example.com/test-post"
    # asyncio.run(send_telegram_notification(test_title, test_url))
    print("Telegram publisher test script. Uncomment the line above to run a live test.")

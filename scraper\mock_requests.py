"""
Mock requests module for testing and development.
This module provides a simplified version of the requests library for testing purposes.
"""

import requests as real_requests
from utils.logger import logger

def get(url, **kwargs):
    """
    Mock implementation of requests.get that logs the request and passes it to the real requests library.
    
    Args:
        url (str): The URL to request
        **kwargs: Additional arguments to pass to requests.get
        
    Returns:
        requests.Response: The response from the real requests library
    """
    logger.debug(f"Mock requests: GET request to {url}")
    return real_requests.get(url, **kwargs)

def post(url, **kwargs):
    """
    Mock implementation of requests.post that logs the request and passes it to the real requests library.
    
    Args:
        url (str): The URL to request
        **kwargs: Additional arguments to pass to requests.post
        
    Returns:
        requests.Response: The response from the real requests library
    """
    logger.debug(f"Mock requests: POST request to {url}")
    return real_requests.post(url, **kwargs)

# Add other request methods as needed

# Import exceptions from the real requests library
exceptions = real_requests.exceptions
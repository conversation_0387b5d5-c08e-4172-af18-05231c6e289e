import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.gemini_manager import gemini_manager
from utils.logger import logger
import re

def clean_article_title(title):
    """Clean and format article title for better readability."""
    if not title:
        return "خبر رياضي مهم"

    # Remove timestamps and dates (improved patterns)
    title = re.sub(r'\d{1,2}:\d{2}\d{1,2}\s*(يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر)\s*\d{4}', '', title)
    title = re.sub(r'\d{1,2}:\d{2}', '', title)
    title = re.sub(r'\d{4}', '', title)
    title = re.sub(r'\d{1,2}\s*(يناير|فبراير|مارس|أبريل|مايو|يونيو|يوليو|أغسطس|سبتمبر|أكتوبر|نوفمبر|ديسمبر)\s*\d{4}', '', title)

    # Remove category prefixes (expanded list)
    prefixes_to_remove = [
        'الإنتقالات', 'تنس', 'الدوري الإنجليزي الممتاز', 'الدوري الإسباني',
        'الدوري الإيطالي', 'الدوري الألماني', 'الدوري الفرنسي', 'الهلال',
        'باريس سان جيرمان ضد ريال مدريد', 'كرة القدم', 'الرياضة', 'أخبار رياضية',
        'عاجل', 'حصري', 'خبر عاجل'
    ]

    for prefix in prefixes_to_remove:
        if title.startswith(prefix):
            title = title[len(prefix):].strip()

    # Remove common unwanted patterns
    title = re.sub(r'^[:\-\|]+\s*', '', title)  # Remove leading colons, dashes, pipes
    title = re.sub(r'\s*[:\-\|]+$', '', title)  # Remove trailing colons, dashes, pipes

    # Clean up extra spaces and characters
    title = re.sub(r'\s+', ' ', title)  # Multiple spaces to single
    title = title.replace('..', '.').strip()

    # Remove weird character combinations
    title = re.sub(r'[^\w\s\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]', ' ', title)
    title = re.sub(r'\s+', ' ', title).strip()

    # Ensure reasonable length
    if len(title) > 80:
        title = title[:77] + "..."

    # If title becomes too short, add context
    if len(title) < 10:
        title = f"خبر رياضي: {title}"

    return title

def generate_seo_keywords(topic):
    """Generates SEO keywords for a given topic using Gemini."""
    try:
        model = gemini_manager.get_model()
        prompt = f"""
أعطني 8 كلمات مفتاحية قوية لمحركات البحث لمقال عن: '{topic}'

المطلوب:
- كلمات مفتاحية قصيرة ومحددة
- تركز على أسماء اللاعبين والأندية
- مناسبة للبحث العربي
- مفصولة بفاصلة فقط

مثال: ميسي, برشلونة, الدوري الإسباني, أهداف

الكلمات المفتاحية:
        """
        response = model.generate_content(prompt)
        keywords = [kw.strip() for kw in response.text.strip().split(',') if kw.strip()]
        return keywords[:8]  # Limit to 8 keywords
    except Exception as e:
        logger.error(f"Failed to generate SEO keywords: {e}")
        return [topic] # Fallback to the topic itself

def generate_catchy_title(original_title):
    """Generate a catchy, click-worthy title using Gemini."""
    try:
        model = gemini_manager.get_model()

        prompt = f"""
أنت خبير في كتابة العناوين الجذابة لأخبار كرة القدم. مهمتك تحويل العنوان التالي إلى عنوان جذاب ومحفز للنقر.

**العنوان الأصلي:** {original_title}

**المطلوب:**
- عنوان واحد فقط (لا تكتب أي شيء آخر)
- أقل من 50 حرف
- جذاب ومحفز للنقر
- يركز على العنصر الأهم في الخبر
- يستخدم أسماء اللاعبين/الأندية المشهورة

**أمثلة للعناوين الجيدة:**
- "ميسي يسجل هدفاً تاريخياً"
- "ريال مدريد يصدم الجماهير"
- "صلاح يقود ليفربول للفوز"
- "برشلونة يضم نجماً جديداً"
- "الأهلي يحسم الديربي"

**العنوان الجديد:**
        """

        response = model.generate_content(prompt)
        new_title = response.text.strip()

        # Clean the response to get only the title
        new_title = new_title.replace('العنوان الجديد:', '').strip()
        new_title = new_title.replace('**', '').strip()
        new_title = new_title.split('\n')[0].strip()  # Take only first line

        # Ensure reasonable length
        if len(new_title) > 60:
            new_title = new_title[:57] + "..."

        # If generation failed, use cleaned original
        if len(new_title) < 10:
            new_title = clean_article_title(original_title)

        logger.info(f"Generated catchy title: '{new_title}' from '{original_title}'")
        return new_title

    except Exception as e:
        logger.error(f"Failed to generate catchy title: {e}")
        return clean_article_title(original_title)

def generate_article(title, source):
    """Generates a high-quality article in Arabic using Gemini."""
    logger.info(f"Generating article for: '{title}'")

    # Generate a catchy title first
    catchy_title = generate_catchy_title(title)
    logger.info(f"Generated catchy title: '{catchy_title}'")

    keywords = generate_seo_keywords(catchy_title)
    logger.info(f"Generated SEO keywords: {keywords}")

    try:
        model = gemini_manager.get_model()

        # Final prompt based on user feedback for a journalistic Egyptian style
        prompt = f"""
أنت صحفي رياضي مصري محترف، مهمتك كتابة مقال تحليلي قوي عن الخبر التالي، بنفس الأسلوب والجودة العالية للمثال المرفق.

**العنوان:** {catchy_title}
**الخبر الأساسي:** {title}

**التعليمات الصارمة:**

1.  **الأسلوب:** اتبع بدقة أسلوب المثال التالي. يجب أن يكون المقال احترافيًا في هيكله، ولكنه مكتوب بلهجة مصرية عامية ومفهومة ومผสมة ببعض الكلمات الفصحى عند اللزوم.
2.  **الهيكل:** يجب أن يحتوي المقال على مقدمة جذابة، وعناوين فرعية واضحة (مثل: "تفاصيل الصفقة"، "تحليل الأسباب"، "التأثير المستقبلي"، "الخلاصة").
3.  **المحتوى:** قدم تحليلًا عميقًا، لا تكتفِ بسرد الخبر. تحدث عن الكواليس، الأسباب المحتملة (فنية، إدارية، رغبة اللاعب)، والتأثير المالي والفني على النادي.
4.  **الطول:** المقال يجب أن يكون حوالي **400-450 كلمة**.
5.  **المصادر:** **ممنوع منعًا باتًا** ذكر أي مصدر إخباري (مثل كووورة أو غيره). أنت تحلل الخبر، لا تنقله.
6.  **الأخطاء الإملائية:** تجنب الأخطاء الإملائية الواضحة، لكن استخدم الصياغة العامية الطبيعية (مثال: "حتكون" بدلاً من "ستكون"، "عشان" بدلاً من "لأن").

**مثال للأسلوب المطلوب (يجب اتباعه بدقة):**
---
**قنبلة في مدريد: الريال بيفرط في نجم من نجومه والقصة فيها كلام كتير**

في خطوة محدش كان يتوقعها خالص، ريال مدريد عملها وأعلن عن قرار عمل ضجة كبيرة في الساحة الرياضية، وهو الموافقة على إعارة واحد من أهم نجومه. القرار الصادم ده بيفتح باب الأسئلة والتكهنات، ومحتاج تحليل دقيق عشان نفهم أبعاده وتأثيره على مستقبل النادي الملكى.

**## تفاصيل الصفقة الغامضة**

لحد دلوقتي، النادي متكتم جدًا على التفاصيل، لا قالوا اسم اللاعب ولا النادي اللي رايحه. بس كل الكلام بيأكد إنه واحد من أعمدة الفريق الأساسية اللي كان ليهم دور كبير في نجاحات الريال السنين اللي فاتت. والأغلب إنه رايح لنادي كبير في أوروبا، ودي حتكون فرصة كويسة للاعب نفسه يثبت قدراته من جديد.

**## ايه اللي ورا الكواليس؟ تحليل أسباب القرار**

طيب، ليه ريال مدريد أخد القرار ده؟ فيه كذا سيناريو محتمل للموضوع ده:
*   **أسباب فنية بحتة:** ممكن يكون المدرب الجديد له فكر تاني، وأسلوب لعب النجم ده مش ماشي مع خطته.
*   **دوافع إدارية:** جايز تكون فيه مشاكل على تجديد العقد أو المرتب، أو يمكن الإدارة عايزة تخفف الحمل المالي.
*   **رغبة اللاعب نفسه:** مش بعيد يكون اللاعب هو اللي طلب يمشي عشان يلاقي فرصة يلعب أساسي في حتة تانية.

**## التأثير على مستقبل النادي الملكى**

الأكيد إن القرار ده هيأثر على الفريق جامد. الريال هيفتقد خبرة ومهارات لاعب تقيل، وده هيحط ضغط كبير على الإدارة عشان تتحرك بسرعة في السوق للبحث عن بديل. أي صفقة جديدة هتحتاج ميزانية ضخمة، وده ممكن يأثر على خطط النادي المالية.

**## الخلاصة: فرصة وتحدي**

الموضوع ده ليه وشين؛ فرصة كبيرة للاعب يثبت نفسه من جديد، وفي نفس الوقت اختبار حقيقي لقدرة ريال مدريد على تعويض غيابه. الفترة الجاية حتكون حاسمة وهتورينا القرار ده كان صح ولا لأ.
---

**ابدأ الآن في كتابة المقال عن ( {catchy_title} ) بنفس الأسلوب الاحترافي والجذاب.**
        """

        response = model.generate_content(prompt)
        article_content = response.text
        logger.info(f"Successfully generated article for '{catchy_title}'.")
        return article_content, keywords, catchy_title

    except Exception as e:
        logger.error(f"Failed to generate article for '{title}': {e}")
        return None, [], catchy_title

if __name__ == '__main__':
    # For testing purposes (requires Gemini API keys in .env)
    test_title = "الأهلي يفوز على الزمالك في قمة الدوري المصري"
    test_source = "Kooora"
    article, keywords = generate_article(test_title, test_source)
    if article:
        print(f"**Article for: {test_title}**")
        print(f"**Keywords:** {keywords}")
        print("---" + " Article Start ---")
        print(article)
        print(f"\n---" + " Article End ---\n")

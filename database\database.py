
import sqlite3
from config import D<PERSON><PERSON><PERSON>E_NAME

def init_db():
    """Initializes the database and creates all necessary tables."""
    with sqlite3.connect(DATABASE_NAME) as conn:
        cursor = conn.cursor()

        # Original news table for scraped articles
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS news (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                url TEXT NOT NULL UNIQUE,
                source TEXT NOT NULL,
                published_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # New table for published articles with their blog URLs
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS published_articles (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                blog_url TEXT NOT NULL UNIQUE,
                original_news_id INTEGER,
                keywords TEXT,
                content_preview TEXT,
                published_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                <PERSON>OREI<PERSON><PERSON>EY (original_news_id) REFERENCES news (id)
            )
        ''')

        # Table for indexing entities (players, teams, competitions) in articles
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS article_entities (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                article_id INTEGER NOT NULL,
                entity_name TEXT NOT NULL,
                entity_type TEXT NOT NULL, -- 'player', 'team', 'competition'
                entity_name_normalized TEXT NOT NULL, -- for better matching
                FOREIGN KEY (article_id) REFERENCES published_articles (id)
            )
        ''')

        # Create indexes separately (SQLite doesn't support INDEX in CREATE TABLE)
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_entity_name_normalized ON article_entities(entity_name_normalized)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_entity_type ON article_entities(entity_type)')

        conn.commit()

def add_news_article(title, url, source):
    """Adds a new news article to the database.

    Args:
        title (str): The title of the article.
        url (str): The URL of the article.
        source (str): The source of the news (e.g., 'Sky News', 'Kooora').

    Returns:
        bool: True if the article was added, False if it already exists.
    """
    try:
        with sqlite3.connect(DATABASE_NAME) as conn:
            cursor = conn.cursor()
            cursor.execute('INSERT INTO news (title, url, source) VALUES (?, ?, ?)', (title, url, source))
            conn.commit()
            return True
    except sqlite3.IntegrityError:
        # This error occurs if the URL (which is UNIQUE) already exists.
        return False

def article_exists(url):
    """Checks if an article with the given URL already exists in the database."""
    with sqlite3.connect(DATABASE_NAME) as conn:
        cursor = conn.cursor()
        cursor.execute('SELECT id FROM news WHERE url = ?', (url,))
        return cursor.fetchone() is not None

def add_published_article(title, blog_url, original_news_id, keywords, content_preview):
    """Adds a published article to the database.

    Args:
        title (str): The title of the published article.
        blog_url (str): The URL of the published blog post.
        original_news_id (int): ID of the original news article.
        keywords (list): List of keywords for the article.
        content_preview (str): First 200 characters of the article content.

    Returns:
        int: The ID of the inserted article, or None if failed.
    """
    try:
        with sqlite3.connect(DATABASE_NAME) as conn:
            cursor = conn.cursor()
            keywords_str = ','.join(keywords) if keywords else ''
            cursor.execute('''
                INSERT INTO published_articles (title, blog_url, original_news_id, keywords, content_preview)
                VALUES (?, ?, ?, ?, ?)
            ''', (title, blog_url, original_news_id, keywords_str, content_preview))
            conn.commit()
            return cursor.lastrowid
    except sqlite3.IntegrityError:
        # Blog URL already exists
        return None
    except Exception as e:
        print(f"Error adding published article: {e}")
        return None

def add_article_entities(article_id, entities):
    """Adds entities (players, teams, competitions) for an article.

    Args:
        article_id (int): ID of the published article.
        entities (list): List of dictionaries with 'name', 'type' keys.
    """
    try:
        with sqlite3.connect(DATABASE_NAME) as conn:
            cursor = conn.cursor()
            for entity in entities:
                entity_name = entity['name']
                entity_type = entity['type']
                # Normalize name for better matching (lowercase, remove extra spaces)
                normalized_name = ' '.join(entity_name.lower().split())

                cursor.execute('''
                    INSERT INTO article_entities (article_id, entity_name, entity_type, entity_name_normalized)
                    VALUES (?, ?, ?, ?)
                ''', (article_id, entity_name, entity_type, normalized_name))
            conn.commit()
    except Exception as e:
        print(f"Error adding article entities: {e}")

def find_articles_by_entity(entity_name, entity_type=None, limit=3):
    """Finds published articles that mention a specific entity.

    Args:
        entity_name (str): Name of the entity to search for.
        entity_type (str, optional): Type of entity ('player', 'team', 'competition').
        limit (int): Maximum number of articles to return.

    Returns:
        list: List of dictionaries with article info (title, blog_url, published_at).
    """
    try:
        with sqlite3.connect(DATABASE_NAME) as conn:
            cursor = conn.cursor()
            normalized_name = ' '.join(entity_name.lower().split())

            if entity_type:
                cursor.execute('''
                    SELECT DISTINCT pa.title, pa.blog_url, pa.published_at
                    FROM published_articles pa
                    JOIN article_entities ae ON pa.id = ae.article_id
                    WHERE ae.entity_name_normalized LIKE ? AND ae.entity_type = ?
                    ORDER BY pa.published_at DESC
                    LIMIT ?
                ''', (f'%{normalized_name}%', entity_type, limit))
            else:
                cursor.execute('''
                    SELECT DISTINCT pa.title, pa.blog_url, pa.published_at
                    FROM published_articles pa
                    JOIN article_entities ae ON pa.id = ae.article_id
                    WHERE ae.entity_name_normalized LIKE ?
                    ORDER BY pa.published_at DESC
                    LIMIT ?
                ''', (f'%{normalized_name}%', limit))

            results = cursor.fetchall()
            return [{'title': row[0], 'blog_url': row[1], 'published_at': row[2]} for row in results]
    except Exception as e:
        print(f"Error finding articles by entity: {e}")
        return []

def get_news_article_id(url):
    """Gets the ID of a news article by its URL.

    Args:
        url (str): The URL of the original news article.

    Returns:
        int: The ID of the news article, or None if not found.
    """
    try:
        with sqlite3.connect(DATABASE_NAME) as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT id FROM news WHERE url = ?', (url,))
            result = cursor.fetchone()
            return result[0] if result else None
    except Exception as e:
        print(f"Error getting news article ID: {e}")
        return None

if __name__ == '__main__':
    # Initialize the database when the script is run directly
    init_db()
    print(f"Database '{DATABASE_NAME}' initialized successfully.")

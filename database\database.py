
import sqlite3
from config import DATABASE_NAME

def init_db():
    """Initializes the database and creates the news table if it doesn't exist."""
    with sqlite3.connect(DATABASE_NAME) as conn:
        cursor = conn.cursor()
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS news (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                url TEXT NOT NULL UNIQUE,
                source TEXT NOT NULL,
                published_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        conn.commit()

def add_news_article(title, url, source):
    """Adds a new news article to the database.

    Args:
        title (str): The title of the article.
        url (str): The URL of the article.
        source (str): The source of the news (e.g., 'Sky News', 'Kooora').

    Returns:
        bool: True if the article was added, False if it already exists.
    """
    try:
        with sqlite3.connect(DATABASE_NAME) as conn:
            cursor = conn.cursor()
            cursor.execute('INSERT INTO news (title, url, source) VALUES (?, ?, ?)', (title, url, source))
            conn.commit()
            return True
    except sqlite3.IntegrityError:
        # This error occurs if the URL (which is UNIQUE) already exists.
        return False

def article_exists(url):
    """Checks if an article with the given URL already exists in the database."""
    with sqlite3.connect(DATABASE_NAME) as conn:
        cursor = conn.cursor()
        cursor.execute('SELECT id FROM news WHERE url = ?', (url,))
        return cursor.fetchone() is not None

if __name__ == '__main__':
    # Initialize the database when the script is run directly
    init_db()
    print(f"Database '{DATABASE_NAME}' initialized successfully.")

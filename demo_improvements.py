#!/usr/bin/env python3
"""
Demo script to show the improvements made to content generation
"""

from generator.content_generator import clean_article_title
from utils.logger import logger

def demo_title_improvements():
    """Demonstrate title cleaning improvements."""
    print("=" * 80)
    print("🔧 تحسينات تنظيف العناوين")
    print("=" * 80)
    
    # Examples of problematic titles
    problematic_titles = [
        {
            "original": "الإنتقالاتأتالانتا يرفض عرضًا ضخمًا من القادسية لبيع ريتيجيالنادي الإيطالي يتمسك بمطالبه12:428 يوليو 2025",
            "description": "عنوان معقد مع تاريخ ونص مدمج"
        },
        {
            "original": "الدوري الإنجليزي الممتازمانشستر يونايتد يفوز على ليفربول",
            "description": "بادئة طويلة مع نص مدمج"
        },
        {
            "original": "عاجل: ميسي يسجل هدفًا رائعًا في مباراة برشلونة",
            "description": "بادئة 'عاجل' غير مرغوبة"
        },
        {
            "original": "12:45 8 يوليو 2025 ريال مدريد يتعاقد مع لاعب جديد",
            "description": "يبدأ بتاريخ ووقت"
        },
        {
            "original": "كرة القدم: الأهلي يفوز على الزمالك في الدوري المصري",
            "description": "بادئة عامة"
        }
    ]
    
    for i, example in enumerate(problematic_titles, 1):
        print(f"\n📝 مثال {i}: {example['description']}")
        print("-" * 60)
        print(f"❌ العنوان الأصلي:")
        print(f"   {example['original']}")
        
        cleaned = clean_article_title(example['original'])
        print(f"✅ العنوان المحسن:")
        print(f"   {cleaned}")
        
        # Show improvements
        improvements = []
        if len(cleaned) < len(example['original']):
            improvements.append(f"تقليل الطول من {len(example['original'])} إلى {len(cleaned)} حرف")
        if "الإنتقالات" not in cleaned and "الإنتقالات" in example['original']:
            improvements.append("إزالة بادئة 'الإنتقالات'")
        if "عاجل" not in cleaned and "عاجل" in example['original']:
            improvements.append("إزالة بادئة 'عاجل'")
        if ":" not in cleaned and ":" in example['original']:
            improvements.append("إزالة علامات الترقيم الزائدة")
        
        if improvements:
            print(f"🔧 التحسينات المطبقة:")
            for improvement in improvements:
                print(f"   • {improvement}")

def demo_content_improvements():
    """Demonstrate content generation improvements."""
    print("\n" + "=" * 80)
    print("📝 تحسينات توليد المحتوى")
    print("=" * 80)
    
    print("\n🔴 المشاكل في النظام القديم:")
    old_problems = [
        "عناوين طويلة ومعقدة مع تواريخ وأرقام",
        "محتوى عام مليء بعبارات مثل 'في عالم كرة القدم المليء بالمفاجآت'",
        "جداول معقدة وتنسيق صعب القراءة",
        "كلمات مفتاحية عامة وغير مفيدة",
        "نصوص طويلة مليئة بالحشو"
    ]
    
    for i, problem in enumerate(old_problems, 1):
        print(f"   {i}. {problem}")
    
    print("\n✅ الحلول المطبقة:")
    solutions = [
        "تنظيف العناوين وإزالة التواريخ والبادئات غير المرغوبة",
        "prompt محدد يركز على الحقائق والتفاصيل المهمة",
        "تنسيق بسيط وواضح بدون جداول معقدة",
        "كلمات مفتاحية محددة تركز على أسماء اللاعبين والأندية",
        "محتوى مركز ومفيد (800-1200 كلمة)"
    ]
    
    for i, solution in enumerate(solutions, 1):
        print(f"   {i}. {solution}")

def demo_prompt_comparison():
    """Show the difference between old and new prompts."""
    print("\n" + "=" * 80)
    print("🔄 مقارنة الـ Prompts")
    print("=" * 80)
    
    print("\n❌ الـ Prompt القديم (عام وغير محدد):")
    old_prompt = """
    **مهمتك:** أنت كاتب صحفي رياضي خبير، متخصص في كرة القدم العربية والعالمية.
    **الموضوع:** {title}
    **المصدر:** {source}

    **المطلوب:**
    1. **عنوان جذاب:** اكتب عنوانًا جديدًا ومختصرًا وجذابًا للخبر يثير فضول القارئ.
    2. **مقال احترافي:** اكتب مقالًا صحفيًا باللغة العربية الفصحى، بأسلوب شيق ومحترف.
    """
    print(old_prompt)
    
    print("\n✅ الـ Prompt الجديد (محدد ومفصل):")
    new_prompt = """
    أنت كاتب صحفي رياضي محترف متخصص في كرة القدم. مهمتك كتابة مقال إخباري احترافي.

    **التعليمات المحددة:**
    1. **العنوان الجديد (مطلوب):**
       - اكتب عنوانًا جذابًا ومختصرًا (أقل من 60 حرف)
       - يجب أن يكون محفزًا للنقر
       - استخدم أسماء اللاعبين والأندية المشهورة

    2. **المقال (800-1200 كلمة):**
       - ابدأ بمقدمة قوية تلخص الخبر في جملتين
       - اكتب 3-4 فقرات رئيسية مع عناوين فرعية
       - استخدم معلومات واقعية ومنطقية
       - تجنب العبارات العامة مثل "في عالم كرة القدم المليء بالمفاجآت"
    """
    print(new_prompt)

def main():
    """Main demo function."""
    print("🚀 عرض توضيحي للتحسينات المطبقة على مولد المحتوى")
    print("تم تطوير هذه التحسينات لحل مشاكل العناوين السيئة والمحتوى الضعيف")
    
    # Demo title improvements
    demo_title_improvements()
    
    # Demo content improvements
    demo_content_improvements()
    
    # Demo prompt comparison
    demo_prompt_comparison()
    
    print("\n" + "=" * 80)
    print("🎯 النتائج المتوقعة:")
    print("=" * 80)
    print("✅ عناوين أقصر وأكثر جاذبية")
    print("✅ محتوى مركز ومفيد")
    print("✅ تنسيق بسيط وواضح")
    print("✅ كلمات مفتاحية محددة ومفيدة")
    print("✅ تجربة قراءة أفضل للمستخدمين")
    
    print("\n📋 للاختبار:")
    print("python test_improved_content.py          # اختبار تنظيف العناوين")
    print("python test_improved_content.py --full   # اختبار كامل مع توليد المحتوى")

if __name__ == '__main__':
    main()

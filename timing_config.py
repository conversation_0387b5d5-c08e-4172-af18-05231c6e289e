# Timing Configuration for Football News Bot

# Article processing timing
ARTICLES_PER_CYCLE = 1  # Only 1 article per cycle to respect timing
DELAY_BETWEEN_ARTICLES = 1800  # 30 minutes in seconds
CYCLE_INTERVAL = 1800  # 30 minutes between cycles

# Quality control
MAX_ARTICLE_AGE_HOURS = 24  # Only process articles from last 24 hours
MIN_TITLE_LENGTH = 20  # Minimum title length
MAX_TITLE_LENGTH = 150  # Maximum title length
ARTICLE_LENGTH_WORDS = "600-800"  # Target article length

# News freshness indicators
TODAY_INDICATORS = [
    'اليوم', 'الآن', 'عاجل', 'حديثاً', 'منذ قليل',
    'منذ دقائق', 'منذ ساعة', 'هذا الصباح', 'مساء اليوم'
]

OLD_NEWS_INDICATORS = [
    'أمس', 'البارحة', 'الأسبوع الماضي', 'الشهر الماضي',
    'منذ أسبوع', 'منذ شهر', 'الأسبوع المنصرم', 'في وقت سابق'
]

# Priority scoring for articles
PRIORITY_WEIGHTS = {
    'has_today_indicator': 10,
    'has_news_url': 5,
    'has_football_content': 3,
    'has_famous_player': 7,
    'has_famous_club': 5
}

# Famous players and clubs for priority scoring
FAMOUS_PLAYERS = [
    'ميسي', 'رونالدو', 'صلاح', 'نيمار', 'مبابي', 'هالاند',
    'بنزيما', 'مودريتش', 'دي بروين', 'كين'
]

FAMOUS_CLUBS = [
    'ريال مدريد', 'برشلونة', 'الأهلي', 'الزمالك', 'النصر', 'الهلال',
    'ليفربول', 'مانشستر', 'تشيلسي', 'أرسنال', 'باريس', 'ميلان',
    'يوفنتوس', 'بايرن', 'أتلتيكو'
]

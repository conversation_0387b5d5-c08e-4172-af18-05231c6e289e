# 🔐 دليل إعداد مصادقة Blogger

## 📋 نظرة عامة

هذا الدليل يشرح كيفية إعداد مصادقة Google OAuth لنشر المقالات على Blogger تلقائياً.

---

## 🚀 الخطوات السريعة

### 1. إنشاء مشروع في Google Cloud Console

1. **اذهب إلى Google Cloud Console:**
   - افتح https://console.cloud.google.com/
   - سجل دخولك بحساب Google

2. **إنشاء مشروع جديد:**
   - اضغط على "Select a project" في الأعلى
   - اضغط "NEW PROJECT"
   - اكتب اسم المشروع (مثل: "Football News Bot")
   - اضغط "CREATE"

### 2. تفعيل Blogger API

1. **اذهب إلى APIs & Services:**
   - من القائمة الجانبية، اختر "APIs & Services" > "Library"

2. **البحث عن Blogger API:**
   - ابح<PERSON> عن "Blogger API v3"
   - اضغط عليه ثم اضغط "ENABLE"

### 3. إنشاء OAuth 2.0 Credentials

1. **اذهب إلى Credentials:**
   - من القائمة الجانبية، اختر "APIs & Services" > "Credentials"

2. **إنشاء OAuth consent screen:**
   - اضغط "OAuth consent screen"
   - اختر "External" ثم "CREATE"
   - املأ المعلومات المطلوبة:
     - App name: "Football News Bot"
     - User support email: بريدك الإلكتروني
     - Developer contact: بريدك الإلكتروني
   - اضغط "SAVE AND CONTINUE"
   - في صفحة "Scopes"، اضغط "SAVE AND CONTINUE"
   - في صفحة "Test users"، أضف بريدك الإلكتروني
   - اضغط "SAVE AND CONTINUE"

3. **إنشاء OAuth 2.0 Client ID:**
   - ارجع إلى "Credentials"
   - اضغط "CREATE CREDENTIALS" > "OAuth 2.0 Client ID"
   - اختر "Desktop application"
   - اكتب اسم (مثل: "Football News Bot Desktop")
   - اضغط "CREATE"

4. **تحميل ملف JSON:**
   - اضغط على أيقونة التحميل بجانب الـ Client ID الجديد
   - احفظ الملف باسم `client_secret.json` في مجلد المشروع

### 4. الحصول على Blog ID

1. **اذهب إلى Blogger:**
   - افتح https://www.blogger.com/
   - اختر المدونة التي تريد النشر عليها

2. **استخراج Blog ID:**
   - من رابط المدونة، ستجد Blog ID
   - مثال: `https://www.blogger.com/blog/posts/1234567890123456789`
   - الرقم `1234567890123456789` هو Blog ID

3. **تحديث ملف .env:**
   ```
   BLOG_ID=1234567890123456789
   ```

---

## 🔧 استخدام النظام الجديد

### التشغيل التفاعلي
```bash
python run_bot.py
```
ثم اختر "3. 🔐 Test Blogger Authentication"

### التشغيل المباشر
```bash
python blogger_auth_manager.py
```

---

## 🔄 عملية المصادقة

### المرة الأولى:
1. سيفتح متصفح الويب تلقائياً
2. سجل دخولك بحساب Google
3. اقبل الصلاحيات المطلوبة
4. سيتم حفظ Token تلقائياً في ملف .env

### المرات التالية:
- النظام سيستخدم Token المحفوظ
- إذا انتهت صلاحية Token، سيتم تجديده تلقائياً
- إذا فشل التجديد، سيطلب مصادقة جديدة

---

## 🧪 اختبار النظام

النظام الجديد يقوم بـ:

### 1. اختبار الاتصال
- التحقق من صحة Token
- الاتصال بـ Blogger API
- عرض معلومات المدونة

### 2. اختبار النشر
- إنشاء منشور تجريبي كـ Draft
- التأكد من إمكانية النشر
- حذف المنشور التجريبي تلقائياً

### 3. إدارة Token
- تجديد Token المنتهي الصلاحية
- حفظ Token الجديد في .env
- معالجة أخطاء المصادقة

---

## 🛡️ الأمان

### حماية الملفات الحساسة:
- `client_secret.json` - لا تشاركه مع أحد
- `.env` - يحتوي على Token الحساس
- أضف هذه الملفات إلى `.gitignore`

### أفضل الممارسات:
- استخدم حساب Google منفصل للبوت
- راجع الصلاحيات بانتظام
- احتفظ بنسخة احتياطية من الإعدادات

---

## 🔧 استكشاف الأخطاء

### خطأ "client_secret.json not found"
- تأكد من وجود الملف في مجلد المشروع
- تأكد من صحة اسم الملف

### خطأ "Invalid client"
- تأكد من صحة ملف client_secret.json
- تأكد من تفعيل Blogger API

### خطأ "Access denied"
- تأكد من إضافة بريدك في Test users
- تأكد من قبول جميع الصلاحيات

### خطأ "Blog not found"
- تأكد من صحة BLOG_ID في ملف .env
- تأكد من أن الحساب له صلاحية على المدونة

---

## 📞 الدعم

إذا واجهت مشاكل:
1. تحقق من ملف logs/bot.log
2. شغل `python blogger_auth_manager.py` للاختبار
3. تأكد من جميع الإعدادات أعلاه

---

## 🎉 النتيجة النهائية

بعد إتمام هذه الخطوات:
- ✅ النظام سيتصل بـ Blogger تلقائياً
- ✅ سيتم تجديد Token تلقائياً
- ✅ سيتم نشر المقالات بدون تدخل يدوي
- ✅ النظام سيعمل بشكل مستقل تماماً

**🚀 النظام جاهز للعمل!**

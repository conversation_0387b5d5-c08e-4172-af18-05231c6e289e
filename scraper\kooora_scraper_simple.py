from scraper.mock_requests import get, exceptions
from bs4 import BeautifulSoup
from config import KOOORA_URL
from utils.logger import logger
import time
import random

def scrape_kooora():
    """Scrapes the latest football news from Kooora using requests (faster alternative).

    Returns:
        list: A list of dictionaries, where each dictionary represents a news article
              with 'title', 'url', and 'source' keys.
    """
    logger.info("Starting simple scrape for Kooora.")
    articles = []
    
    try:
        # Add headers to mimic a real browser
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'ar,en-US;q=0.7,en;q=0.3',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
        
        response = get(KOOORA_URL, headers=headers, timeout=30)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Try to find the latest news section first
        latest_news_selectors = [
            '.latest-news a',  # Latest news section
            '.breaking-news a',  # Breaking news
            '.recent-news a',  # Recent news
            '.news-list a',  # News list
            '.main-news a',  # Main news
            '.top-news a',  # Top news
            'section[class*="news"] a',  # News sections
            'div[class*="news"] a',  # News divs
            '.content a[href*="/news/"]',  # News links in content
            'main a[href*="/news/"]'  # News links in main
        ]

        # Try multiple selectors to find news articles
        selectors_to_try = [
            'a[href*="/news/"]',  # News links
            'a[href*="/sport/"]',  # Sport links
            'a[href*="/football/"]',  # Football links
            '.news-item a',
            '.news-title a',
            'article a',
            '.article-title a',
            'h3 a',
            'h2 a',
            '.title a'
        ]

        # Combine latest news selectors with general selectors
        all_selectors = latest_news_selectors + selectors_to_try
        
        found_articles = []

        for selector in all_selectors:
            try:
                logger.info(f"Trying selector: {selector}")
                links = soup.select(selector)

                for link in links:
                    try:
                        href = link.get('href')
                        text = link.get_text(strip=True)
                        title_attr = link.get('title', '')

                        # Skip if no href or text
                        if not href or not text:
                            continue

                        # Skip navigation and non-news items
                        skip_keywords = [
                            'تسجيل', 'دخول', 'حساب', 'إعدادات', 'بحث', 'قائمة',
                            'الرئيسية', 'اتصل', 'عنا', 'سياسة', 'شروط', 'خصوصية',
                            'login', 'register', 'search', 'menu', 'home', 'contact',
                            'about', 'privacy', 'terms', 'policy'
                        ]

                        if any(skip in text.lower() for skip in skip_keywords):
                            continue

                        # Make URL absolute
                        if href.startswith('/'):
                            href = f"https://www.kooora.com{href}"
                        elif not href.startswith('http'):
                            continue

                        # Check if this looks like a real news article
                        # Must have news indicators in URL or be a substantial article
                        url_news_indicators = ['/news/', '/sport/', '/football/', '/match/']
                        has_news_url = any(indicator in href.lower() for indicator in url_news_indicators)

                        # Check for football content keywords (expanded list)
                        football_keywords = [
                            'كرة', 'مباراة', 'فوز', 'هدف', 'لاعب', 'فريق', 'دوري', 'كأس',
                            'بطولة', 'مدرب', 'نادي', 'منتخب', 'ملعب', 'حكم', 'إصابة',
                            'انتقال', 'صفقة', 'عقد', 'تجديد', 'استقالة', 'إقالة',
                            'ميسي', 'رونالدو', 'صلاح', 'ريال مدريد', 'برشلونة', 'الأهلي',
                            'الزمالك', 'النصر', 'الهلال', 'ليفربول', 'مانشستر', 'تشيلسي',
                            'أرسنال', 'باريس', 'ميلان', 'يوفنتوس', 'بايرن', 'أتلتيكو'
                        ]

                        has_football_content = any(keyword in text.lower() or keyword in title_attr.lower()
                                                 for keyword in football_keywords)

                        # Additional quality filters
                        # Skip old news indicators (expanded list)
                        old_news_indicators = [
                            'أمس', 'البارحة', 'الأسبوع الماضي', 'الشهر الماضي',
                            'yesterday', 'last week', 'last month', 'منذ أسبوع',
                            'منذ شهر', 'الأسبوع المنصرم', 'في وقت سابق'
                        ]

                        is_old_news = any(indicator in text.lower() for indicator in old_news_indicators)

                        # Check for today's date indicators
                        import datetime
                        today = datetime.date.today()
                        today_indicators = [
                            'اليوم', 'الآن', 'عاجل', 'حديثاً', 'منذ قليل',
                            f'{today.day} يوليو', 'يوليو 2025', '8 يوليو'
                        ]

                        has_today_indicator = any(indicator in text.lower() for indicator in today_indicators)

                        # Skip generic/low-quality titles
                        low_quality_indicators = [
                            'اقرأ المزيد', 'تفاصيل أكثر', 'شاهد الفيديو', 'صور',
                            'read more', 'click here', 'watch video'
                        ]

                        is_low_quality = any(indicator in text.lower() for indicator in low_quality_indicators)

                        # Filter for quality articles (improved criteria with today's news priority)
                        if (len(text) > 20 and len(text) < 150 and  # Substantial but not too long title
                            (has_news_url or has_football_content) and  # Relevant content
                            'kooora.com' in href and  # From Kooora
                            not is_old_news and  # Not old news
                            not is_low_quality and  # Not low quality
                            not any(skip in href.lower() for skip in ['login', 'register', 'search'])):  # Not utility pages

                            # Clean up title (improved cleaning)
                            clean_title = text.replace('\n', ' ').replace('\t', ' ')
                            clean_title = ' '.join(clean_title.split())  # Remove extra whitespace

                            # Remove common prefixes/suffixes (expanded list)
                            prefixes_to_remove = [
                                'كووورة |', '| كووورة', 'كووورة:', 'كووورة -',
                                'عاجل:', 'حصري:', 'خاص:', 'عاجل |', 'حصري |',
                                'الإنتقالات', 'تنس', 'الدوري الإنجليزي الممتاز',
                                'الدوري الإسباني', 'الدوري الإيطالي'
                            ]

                            for prefix in prefixes_to_remove:
                                if clean_title.startswith(prefix):
                                    clean_title = clean_title[len(prefix):].strip()
                                if clean_title.endswith(prefix):
                                    clean_title = clean_title[:-len(prefix)].strip()

                            # Remove timestamps and extra characters
                            import re
                            clean_title = re.sub(r'\d{1,2}:\d{2}', '', clean_title)  # Remove time
                            clean_title = re.sub(r'\d{4}', '', clean_title)  # Remove year
                            clean_title = re.sub(r'[^\w\s\u0600-\u06FF]', ' ', clean_title)  # Keep only Arabic and alphanumeric
                            clean_title = ' '.join(clean_title.split())  # Clean extra spaces

                            if len(clean_title) > 20:  # Ensure substantial content after cleaning
                                found_articles.append({
                                    'title': clean_title,
                                    'url': href,
                                    'source': 'Kooora',
                                    'has_today_indicator': has_today_indicator,
                                    'priority_score': (
                                        10 if has_today_indicator else 0 +
                                        5 if has_news_url else 0 +
                                        3 if has_football_content else 0
                                    )
                                })

                                if len(found_articles) >= 15:  # Get more to have better selection
                                    break

                    except Exception as e:
                        logger.warning(f"Error processing link: {e}")
                        continue

                if found_articles:
                    logger.info(f"Found {len(found_articles)} articles with selector: {selector}")
                    break

            except Exception as e:
                logger.warning(f"Error with selector {selector}: {e}")
                continue
        
        # Remove duplicates and prioritize news articles
        seen_urls = set()
        seen_titles = set()

        # Sort articles to prioritize today's news first
        found_articles.sort(key=lambda x: (
            -x.get('priority_score', 0),  # Higher priority score first
            not x.get('has_today_indicator', False),  # Today's news first
            '/news/' not in x['url'].lower(),  # News articles first
            '/sport/' not in x['url'].lower(),  # Then sport articles
            len(x['title'])  # Then by title length (longer = more detailed)
        ))

        for article in found_articles:
            # Skip duplicates by URL
            if article['url'] in seen_urls:
                continue

            # Skip very similar titles
            title_words = set(article['title'].lower().split())
            is_similar = False
            for seen_title in seen_titles:
                seen_words = set(seen_title.lower().split())
                # If more than 70% of words are the same, consider it duplicate
                if len(title_words & seen_words) / max(len(title_words), len(seen_words)) > 0.7:
                    is_similar = True
                    break

            if not is_similar:
                articles.append(article)
                seen_urls.add(article['url'])
                seen_titles.add(article['title'])
                logger.info(f"Found article: {article['title'][:50]}...")

                # Limit to 10 quality articles
                if len(articles) >= 10:
                    break
                
    except exceptions.RequestException as e:
        logger.error(f"Failed to scrape Kooora: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred during Kooora scraping: {e}")

    logger.info(f"Found {len(articles)} articles from Kooora.")
    return articles

if __name__ == '__main__':
    # For testing purposes
    scraped_articles = scrape_kooora()
    if scraped_articles:
        for article in scraped_articles:
            print(article)

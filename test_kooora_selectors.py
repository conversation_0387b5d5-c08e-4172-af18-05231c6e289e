#!/usr/bin/env python3
"""
Test script to check Kooora website structure and find correct CSS selectors
"""

import requests
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager
import time

def test_with_requests():
    """Test Kooora with requests library"""
    print("=== Testing with requests ===")
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        }
        response = requests.get('https://www.kooora.com/', headers=headers, timeout=30)
        print(f"Status code: {response.status_code}")
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Try different selectors
        selectors_to_test = [
            'a[href*="/news/"]',
            '.news-item',
            '.news-title', 
            'a[href*="kooora.com"]',
            '.article-title',
            'h3 a',
            'h4 a',
            '.title a',
            'a[title]'
        ]
        
        for selector in selectors_to_test:
            elements = soup.select(selector)
            print(f"Selector '{selector}': Found {len(elements)} elements")
            if elements:
                for i, elem in enumerate(elements[:3]):
                    title = elem.get_text(strip=True) or elem.get('title', '')
                    href = elem.get('href', '')
                    print(f"  {i+1}. Title: {title[:50]}... | URL: {href}")
                print()
                
    except Exception as e:
        print(f"Error with requests: {e}")

def test_with_selenium():
    """Test Kooora with Selenium"""
    print("\n=== Testing with Selenium ===")
    
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
    
    driver = None
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        driver.set_page_load_timeout(60)
        
        print("Loading Kooora...")
        driver.get('https://www.kooora.com/')
        time.sleep(5)
        
        print(f"Page title: {driver.title}")
        
        # Try different selectors
        selectors_to_test = [
            'a[href*="/news/"]',
            'a[href*="news"]',
            '.news-item',
            '.news-title', 
            'a[href*="kooora.com"]',
            '.article-title',
            'h3 a',
            'h4 a',
            '.title a',
            'a[title]',
            'a',  # All links
        ]
        
        for selector in selectors_to_test:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                print(f"Selector '{selector}': Found {len(elements)} elements")
                
                if elements:
                    for i, elem in enumerate(elements[:3]):
                        try:
                            title = elem.text.strip() or elem.get_attribute('title') or ''
                            href = elem.get_attribute('href') or ''
                            print(f"  {i+1}. Title: {title[:50]}... | URL: {href}")
                        except:
                            print(f"  {i+1}. Could not extract data")
                    print()
                    
            except Exception as e:
                print(f"Error with selector '{selector}': {e}")
                
    except Exception as e:
        print(f"Error with Selenium: {e}")
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    test_with_requests()
    test_with_selenium()

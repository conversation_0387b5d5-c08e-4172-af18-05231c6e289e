"""
Mock selenium module for testing and development.
This module provides a simplified version of the selenium library for testing purposes.
"""

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager
from utils.logger import logger

# Re-export the selenium classes and functions
# This allows us to add logging or other functionality in the future

# Add logging wrapper if needed
class MockWebDriver:
    def __init__(self, *args, **kwargs):
        logger.debug("Initializing mock webdriver")
        self.real_driver = webdriver.Chrome(*args, **kwargs)
    
    def __getattr__(self, name):
        return getattr(self.real_driver, name)

# For now, just use the real selenium classes
# If we need to add mock functionality later, we can replace these
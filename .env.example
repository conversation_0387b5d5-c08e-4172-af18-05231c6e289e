# Environment Variables for Smart Image Generation
# Copy this file to .env and fill in your API keys

# ===== EXISTING VARIABLES =====
# Google Gemini API Key (required for content generation and image analysis)
GOOGLE_API_KEY=your_google_gemini_api_key_here

# Google OAuth Token for Blogger (required for publishing)
GOOGLE_OAUTH_TOKEN=your_google_oauth_token_json_here

# Blog ID for Blogger (required for publishing)
BLOG_ID=your_blogger_blog_id_here

# Telegram Bot Token (optional for notifications)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# Telegram Chat ID (optional for notifications)
TELEGRAM_CHAT_ID=your_telegram_chat_id_here

# ===== NEW VARIABLES FOR SMART IMAGES =====

# Pixabay API Key (for searching football images)
# Get your free API key from: https://pixabay.com/api/docs/
PIXABAY_API_KEY=your_pixabay_api_key_here

# Unsplash Access Key (for searching high-quality images)
# Get your free access key from: https://unsplash.com/developers
UNSPLASH_ACCESS_KEY=your_unsplash_access_key_here

# Pexels API Key (optional - for additional image sources)
# Get your free API key from: https://www.pexels.com/api/
PEXELS_API_KEY=your_pexels_api_key_here

# ===== IMAGE GENERATION SETTINGS =====

# Enable/disable smart image generation (true/false)
SMART_IMAGES_ENABLED=true

# Minimum AI relevance score for accepting images (0-10)
MIN_RELEVANCE_SCORE=6.0

# Maximum number of images to search per query
MAX_SEARCH_RESULTS=20

# Maximum number of images to analyze with AI (costs API calls)
MAX_ANALYZE_IMAGES=5

# Enable/disable fallback image creation when no suitable image found
FALLBACK_IMAGES_ENABLED=true

# ===== DATABASE SETTINGS =====
DATABASE_NAME=news.db

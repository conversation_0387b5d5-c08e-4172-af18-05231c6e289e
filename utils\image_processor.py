import sys
import os
import requests
from PIL import Image, ImageDraw, ImageFont, ImageFilter
from io import BytesIO
import textwrap

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.font_manager import font_manager
from utils.logger import logger

class ImageProcessor:
    """Processes images by adding overlay and text for football articles."""
    
    def __init__(self):
        self.target_width = 1280
        self.target_height = 720
        self.overlay_opacity = 0.3  # 30% opacity for black overlay
        self.text_margin = 40
        self.max_text_width_ratio = 0.8  # Text can use 80% of image width
    
    def download_image(self, image_url):
        """Download image from URL.
        
        Args:
            image_url (str): URL of the image to download
            
        Returns:
            PIL.Image: Downloaded image, or None if failed
        """
        try:
            response = requests.get(image_url, timeout=15, stream=True)
            response.raise_for_status()
            
            image = Image.open(BytesIO(response.content))
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            logger.info(f"Successfully downloaded image: {image.size}")
            return image
            
        except Exception as e:
            logger.error(f"Failed to download image from {image_url}: {e}")
            return None
    
    def resize_image(self, image, target_width=None, target_height=None):
        """Resize image to target dimensions while maintaining aspect ratio.
        
        Args:
            image (PIL.Image): Image to resize
            target_width (int): Target width (default: self.target_width)
            target_height (int): Target height (default: self.target_height)
            
        Returns:
            PIL.Image: Resized image
        """
        if target_width is None:
            target_width = self.target_width
        if target_height is None:
            target_height = self.target_height
        
        try:
            # Calculate aspect ratios
            original_ratio = image.width / image.height
            target_ratio = target_width / target_height
            
            if original_ratio > target_ratio:
                # Image is wider than target, fit by height
                new_height = target_height
                new_width = int(target_height * original_ratio)
            else:
                # Image is taller than target, fit by width
                new_width = target_width
                new_height = int(target_width / original_ratio)
            
            # Resize image
            resized = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Create final image with target dimensions
            final_image = Image.new('RGB', (target_width, target_height), (0, 0, 0))
            
            # Calculate position to center the resized image
            x = (target_width - new_width) // 2
            y = (target_height - new_height) // 2
            
            # Paste resized image onto final image
            final_image.paste(resized, (x, y))
            
            logger.info(f"Resized image from {image.size} to {final_image.size}")
            return final_image
            
        except Exception as e:
            logger.error(f"Failed to resize image: {e}")
            return image
    
    def add_overlay(self, image, opacity=None):
        """Add semi-transparent black overlay to image.
        
        Args:
            image (PIL.Image): Image to add overlay to
            opacity (float): Overlay opacity (0.0 to 1.0)
            
        Returns:
            PIL.Image: Image with overlay
        """
        if opacity is None:
            opacity = self.overlay_opacity
        
        try:
            # Create overlay
            overlay = Image.new('RGBA', image.size, (0, 0, 0, int(255 * opacity)))
            
            # Convert image to RGBA if needed
            if image.mode != 'RGBA':
                image = image.convert('RGBA')
            
            # Composite overlay onto image
            result = Image.alpha_composite(image, overlay)
            
            # Convert back to RGB
            result = result.convert('RGB')
            
            logger.info("Added overlay to image")
            return result
            
        except Exception as e:
            logger.error(f"Failed to add overlay: {e}")
            return image
    
    def wrap_text(self, text, font, max_width):
        """Wrap text to fit within specified width.
        
        Args:
            text (str): Text to wrap
            font (ImageFont): Font to use for measuring
            max_width (int): Maximum width in pixels
            
        Returns:
            list: List of text lines
        """
        words = text.split()
        lines = []
        current_line = []
        
        for word in words:
            # Test if adding this word would exceed max width
            test_line = ' '.join(current_line + [word])
            bbox = font.getbbox(test_line)
            text_width = bbox[2] - bbox[0]
            
            if text_width <= max_width:
                current_line.append(word)
            else:
                if current_line:
                    lines.append(' '.join(current_line))
                    current_line = [word]
                else:
                    # Single word is too long, add it anyway
                    lines.append(word)
        
        if current_line:
            lines.append(' '.join(current_line))
        
        return lines
    
    def add_text_to_image(self, image, text, font_path=None):
        """Add text overlay to image.
        
        Args:
            image (PIL.Image): Image to add text to
            text (str): Text to add
            font_path (str): Path to font file (optional)
            
        Returns:
            PIL.Image: Image with text overlay
        """
        try:
            # Get font path if not provided
            if not font_path:
                font_path = font_manager.get_font_path(text, bold=True)
            
            if not font_path or not os.path.exists(font_path):
                logger.warning("Font not found, using default font")
                font_path = None
            
            # Calculate font size
            font_size = font_manager.suggest_font_size(text, image.width, image.height)
            
            # Load font
            try:
                if font_path:
                    font = ImageFont.truetype(font_path, font_size)
                else:
                    font = ImageFont.load_default()
            except Exception as e:
                logger.warning(f"Failed to load font {font_path}: {e}")
                font = ImageFont.load_default()
            
            # Calculate text area
            max_text_width = int(image.width * self.max_text_width_ratio)
            
            # Wrap text
            text_lines = self.wrap_text(text, font, max_text_width)
            
            # Calculate total text height
            line_height = font_size + 10  # Add some line spacing
            total_text_height = len(text_lines) * line_height
            
            # Calculate starting position
            start_y = image.height - total_text_height - self.text_margin
            
            # Create drawing context
            draw = ImageDraw.Draw(image)
            
            # Draw each line
            for i, line in enumerate(text_lines):
                # Calculate line position
                bbox = font.getbbox(line)
                text_width = bbox[2] - bbox[0]
                x = (image.width - text_width) // 2  # Center horizontally
                y = start_y + (i * line_height)
                
                # Draw text shadow for better readability
                shadow_offset = 2
                draw.text((x + shadow_offset, y + shadow_offset), line, font=font, fill=(0, 0, 0, 180))
                
                # Draw main text
                draw.text((x, y), line, font=font, fill=(255, 255, 255, 255))
            
            logger.info(f"Added text to image: '{text}' ({len(text_lines)} lines)")
            return image
            
        except Exception as e:
            logger.error(f"Failed to add text to image: {e}")
            return image
    
    def process_image_complete(self, image_url, text, output_path=None):
        """Complete image processing pipeline.
        
        Args:
            image_url (str): URL of the image to process
            text (str): Text to add to the image
            output_path (str): Path to save the processed image
            
        Returns:
            str: Path to the processed image, or None if failed
        """
        try:
            # Download image
            image = self.download_image(image_url)
            if not image:
                return None
            
            # Resize to target dimensions
            image = self.resize_image(image)
            
            # Add overlay
            image = self.add_overlay(image)
            
            # Add text
            image = self.add_text_to_image(image, text)
            
            # Save image
            if not output_path:
                # Generate output path
                import time
                timestamp = int(time.time())
                output_path = f"imgs/processed_image_{timestamp}.jpg"
            
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Save with high quality
            image.save(output_path, 'JPEG', quality=90, optimize=True)
            
            logger.info(f"Processed image saved to: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Failed to process image: {e}")
            return None
    
    def create_fallback_image(self, text, output_path=None):
        """Create a fallback image with just text when no suitable image is found.
        
        Args:
            text (str): Text to display
            output_path (str): Path to save the image
            
        Returns:
            str: Path to the created image
        """
        try:
            # Create base image with gradient background
            image = Image.new('RGB', (self.target_width, self.target_height), (20, 30, 50))
            
            # Create gradient effect
            draw = ImageDraw.Draw(image)
            for y in range(self.target_height):
                color_value = int(20 + (y / self.target_height) * 30)
                draw.line([(0, y), (self.target_width, y)], fill=(color_value, color_value + 10, color_value + 20))
            
            # Add text
            image = self.add_text_to_image(image, text)
            
            # Save image
            if not output_path:
                import time
                timestamp = int(time.time())
                output_path = f"imgs/fallback_image_{timestamp}.jpg"
            
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            image.save(output_path, 'JPEG', quality=90)
            
            logger.info(f"Created fallback image: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Failed to create fallback image: {e}")
            return None


# Create global instance
image_processor = ImageProcessor()

# 🔗 ميزة الربط الداخلي الذكي

## 📋 نظرة عامة

تم إضافة ميزة الربط الداخلي الذكي إلى وكيل أخبار كرة القدم. هذه الميزة تقوم تلقائياً بإضافة روابط داخلية في المقالات الجديدة تشير إلى المقالات السابقة ذات الصلة.

## 🎯 الفوائد

### ✅ تحسين تجربة المستخدم
- القارئ ينتقل بسلاسة من خبر إلى آخر
- سهولة الوصول للمعلومات ذات الصلة

### ✅ زيادة الوقت في الموقع
- الربط الداخلي يحفز القارئ للبقاء لفترة أطول
- زيادة عدد الصفحات المزارة

### ✅ تعزيز SEO الداخلي
- محركات البحث تقيم المواقع المترابطة بذكاء
- تحسين ترتيب الموقع في نتائج البحث

### ✅ إبراز المقالات القديمة
- المقالات السابقة تحصل على زيارات إضافية
- استغلال أفضل للمحتوى الموجود

## 🔧 كيف يعمل النظام

### 1. استخراج الكيانات
النظام يستخدم الذكاء الاصطناعي (Gemini AI) لاستخراج:
- **أسماء اللاعبين**: ميسي، رونالدو، صلاح، مبابي، إلخ
- **أسماء الأندية**: ريال مدريد، برشلونة، الأهلي، ليفربول، إلخ
- **أسماء البطولات**: دوري أبطال أوروبا، الدوري الإنجليزي، كأس العالم، إلخ

### 2. البحث عن المقالات ذات الصلة
- البحث في قاعدة البيانات عن المقالات المنشورة سابقاً
- مطابقة الكيانات المستخرجة مع المقالات الموجودة
- ترتيب النتائج حسب تاريخ النشر (الأحدث أولاً)

### 3. إضافة الروابط الذكية
- إضافة روابط HTML فقط للمقالات الموجودة فعلاً
- تجنب الروابط المكسورة
- حد أقصى 4 روابط لكل مقال لتجنب الإفراط
- ترتيب الكيانات حسب الطول لتجنب التداخل

## 📊 هيكل قاعدة البيانات الجديدة

### جدول `published_articles`
```sql
CREATE TABLE published_articles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    blog_url TEXT NOT NULL UNIQUE,
    original_news_id INTEGER,
    keywords TEXT,
    content_preview TEXT,
    published_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### جدول `article_entities`
```sql
CREATE TABLE article_entities (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    article_id INTEGER NOT NULL,
    entity_name TEXT NOT NULL,
    entity_type TEXT NOT NULL, -- 'player', 'team', 'competition'
    entity_name_normalized TEXT NOT NULL
);
```

## 🚀 مثال عملي

### النص الأصلي:
```
ريال مدريد يعلن عن ضم كيليان مبابي رسمياً من باريس سان جيرمان.
اللاعب الفرنسي سينضم إلى النادي الملكي ويلعب في دوري أبطال أوروبا.
```

### النص مع الروابط الداخلية:
```html
<a href="https://blog.com/real-madrid-news">ريال مدريد</a> يعلن عن ضم 
<a href="https://blog.com/mbappe-transfer">كيليان مبابي</a> رسمياً من 
<a href="https://blog.com/psg-news">باريس سان جيرمان</a>.
اللاعب الفرنسي سينضم إلى النادي الملكي ويلعب في 
<a href="https://blog.com/champions-league">دوري أبطال أوروبا</a>.
```

## 📁 الملفات المضافة/المعدلة

### ملفات جديدة:
- `utils/internal_linking_manager.py` - مدير الربط الداخلي الرئيسي
- `test_internal_linking.py` - ملف اختبار النظام

### ملفات معدلة:
- `database/database.py` - إضافة جداول وفنكشنز جديدة
- `generator/content_generator.py` - دمج الربط الداخلي
- `main.py` - حفظ بيانات المقالات والكيانات

## 🧪 كيفية الاختبار

```bash
# تشغيل اختبار شامل للنظام
python test_internal_linking.py

# اختبار سريع للربط الداخلي
python -c "
from utils.internal_linking_manager import internal_linking_manager
content = 'ميسي سجل هدفاً رائعاً لصالح باريس سان جيرمان في دوري أبطال أوروبا'
linked_content, entities = internal_linking_manager.process_article_for_linking('عنوان تجريبي', content)
print(linked_content)
"
```

## ⚙️ الإعدادات والتخصيص

### تخصيص قوائم الكيانات
يمكن تعديل قوائم اللاعبين والأندية في `utils/internal_linking_manager.py`:

```python
self.common_players = [
    'ميسي', 'رونالدو', 'صلاح', 'مبابي',
    # أضف المزيد من الأسماء هنا
]

self.common_teams = [
    'ريال مدريد', 'برشلونة', 'الأهلي',
    # أضف المزيد من الأندية هنا
]
```

### تخصيص عدد الروابط
```python
# في دالة add_internal_links
if links_added >= 4:  # غير هذا الرقم حسب الحاجة
    break
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

1. **لا تظهر روابط داخلية**
   - تأكد من وجود مقالات سابقة في قاعدة البيانات
   - تحقق من أن الكيانات يتم استخراجها بشكل صحيح

2. **روابط مكسورة**
   - النظام يربط فقط بالمقالات الموجودة في قاعدة البيانات
   - تحقق من صحة URLs في جدول `published_articles`

3. **أخطاء في استخراج الكيانات**
   - تأكد من أن Gemini API يعمل بشكل صحيح
   - النظام يستخدم قوائم احتياطية في حالة فشل AI

## 📈 إحصائيات ومراقبة

النظام يسجل معلومات مفيدة في اللوج:
- عدد الكيانات المستخرجة
- عدد الروابط المضافة
- URLs المقالات المرتبطة

مثال على رسائل اللوج:
```
INFO - Extracted entities: {'players': ['ميسي'], 'teams': ['ريال مدريد']}
INFO - Added internal link for 'ميسي' -> https://blog.com/messi-news
INFO - Successfully added 2 internal links to the article
```

## 🔮 تطويرات مستقبلية

- إضافة ذكاء اصطناعي لتحديد أفضل المقالات للربط
- نظام تقييم جودة الروابط
- إحصائيات تفاعل المستخدمين مع الروابط الداخلية
- ربط تلقائي بالمقالات الخارجية ذات الصلة

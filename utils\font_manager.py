import os
import re
from pathlib import Path

class FontManager:
    """Manages Arabic and English fonts for image text overlay."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.fonts_dir = self.project_root / "fonts"
        self.arabic_fonts_dir = self.fonts_dir / "arabic"
        self.english_fonts_dir = self.fonts_dir / "english"
        
        # Default font mappings
        self.arabic_fonts = {
            'bold': 'NotoSansArabic-Bold.ttf',
            'regular': 'NotoSansArabic-Regular.ttf',
            'fallback_bold': 'Amiri-Bold.ttf',
            'fallback_regular': 'Amiri-Regular.ttf'
        }
        
        self.english_fonts = {
            'bold': 'Roboto-Bold.ttf',
            'regular': 'Roboto-Regular.ttf',
            'fallback_bold': 'OpenSans-Bold.ttf',
            'fallback_regular': 'OpenSans-Regular.ttf'
        }
    
    def is_arabic_text(self, text):
        """Check if text contains Arabic characters."""
        arabic_pattern = re.compile(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]')
        return bool(arabic_pattern.search(text))
    
    def get_font_path(self, text, bold=True):
        """Get the appropriate font path based on text language and style.
        
        Args:
            text (str): The text to analyze
            bold (bool): Whether to use bold font
            
        Returns:
            str: Path to the font file, or None if not found
        """
        is_arabic = self.is_arabic_text(text)
        font_style = 'bold' if bold else 'regular'
        
        if is_arabic:
            fonts_dir = self.arabic_fonts_dir
            font_mapping = self.arabic_fonts
        else:
            fonts_dir = self.english_fonts_dir
            font_mapping = self.english_fonts
        
        # Try primary font first
        primary_font = fonts_dir / font_mapping[font_style]
        if primary_font.exists():
            return str(primary_font)
        
        # Try fallback font
        fallback_font = fonts_dir / font_mapping[f'fallback_{font_style}']
        if fallback_font.exists():
            return str(fallback_font)
        
        # Try any available font in the directory
        available_fonts = list(fonts_dir.glob('*.ttf'))
        if available_fonts:
            return str(available_fonts[0])
        
        return None
    
    def get_available_fonts(self):
        """Get list of all available fonts."""
        fonts = {
            'arabic': [],
            'english': []
        }
        
        if self.arabic_fonts_dir.exists():
            fonts['arabic'] = [f.name for f in self.arabic_fonts_dir.glob('*.ttf')]
        
        if self.english_fonts_dir.exists():
            fonts['english'] = [f.name for f in self.english_fonts_dir.glob('*.ttf')]
        
        return fonts
    
    def check_fonts_availability(self):
        """Check which fonts are available and return status."""
        status = {
            'arabic': {
                'available': [],
                'missing': []
            },
            'english': {
                'available': [],
                'missing': []
            }
        }
        
        # Check Arabic fonts
        for font_type, font_name in self.arabic_fonts.items():
            font_path = self.arabic_fonts_dir / font_name
            if font_path.exists():
                status['arabic']['available'].append(font_name)
            else:
                status['arabic']['missing'].append(font_name)
        
        # Check English fonts
        for font_type, font_name in self.english_fonts.items():
            font_path = self.english_fonts_dir / font_name
            if font_path.exists():
                status['english']['available'].append(font_name)
            else:
                status['english']['missing'].append(font_name)
        
        return status
    
    def suggest_font_size(self, text, image_width, image_height):
        """Suggest appropriate font size based on text length and image dimensions.
        
        Args:
            text (str): The text to display
            image_width (int): Image width in pixels
            image_height (int): Image height in pixels
            
        Returns:
            int: Suggested font size
        """
        # Base font size calculation
        base_size = min(image_width, image_height) // 20
        
        # Adjust based on text length
        text_length = len(text)
        if text_length <= 20:
            multiplier = 1.2  # Larger font for short text
        elif text_length <= 40:
            multiplier = 1.0  # Normal font
        elif text_length <= 60:
            multiplier = 0.8  # Smaller font for longer text
        else:
            multiplier = 0.6  # Much smaller for very long text
        
        font_size = int(base_size * multiplier)
        
        # Ensure minimum and maximum font sizes
        font_size = max(24, min(font_size, 120))
        
        return font_size
    
    def get_text_position(self, text, image_width, image_height, font_size):
        """Calculate optimal text position on image.
        
        Args:
            text (str): The text to position
            image_width (int): Image width in pixels
            image_height (int): Image height in pixels
            font_size (int): Font size
            
        Returns:
            tuple: (x, y) position for text
        """
        # Estimate text dimensions (rough calculation)
        char_width = font_size * 0.6  # Average character width
        text_width = len(text) * char_width
        text_height = font_size
        
        # Position text in lower third of image, centered horizontally
        x = (image_width - text_width) // 2
        y = image_height - (image_height // 4) - text_height
        
        # Ensure text doesn't go outside image bounds
        x = max(20, min(x, image_width - text_width - 20))
        y = max(text_height + 20, min(y, image_height - 20))
        
        return (int(x), int(y))


# Create global instance
font_manager = FontManager()

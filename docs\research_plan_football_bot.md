# خطة البحث: تطوير بوت أخبار كرة القدم الذكي

## الأهداف
- وضع خطة تقنية مفصلة وقابلة للتنفيذ لإنشاء بوت أخبار كرة القدم.
- تحليل المصادر والتقنيات المتاحة لضمان نجاح المشروع ضمن الموارد المحددة.
- تقديم توصيات واضحة للمكتبات والأدوات واستراتيجيات التنفيذ.

## تفصيل البحث
- **تحليل المصادر (20%):**
  - تحليل بنية وهيكلية محتوى موقعي Sky News Arabia و Kooora.
  - تحديد العناصر الأساسية للأخبار (عناوين، نصوص، صور، تواريخ).
- **تحليل APIs والمكتبات (30%):**
  - البحث في وثائق واجهات برمجة التطبيقات (APIs) لـ Blogger, Telegram, و Gemini.
  - تحديد أفضل مكتبات Python للتعامل مع كل API.
  - البحث عن مكتبات لـ SEO، معالجة الصور، وإدارة قواعد البيانات.
- **تصميم النظام والهيكلية (25%):**
  - تصميم هيكل النظام العام والمكونات الرئيسية.
  - وضع استراتيجية لإدارة مفاتيح Gemini API.
  - تصميم مخطط قاعدة البيانات لتجنب تكرار الأخبار.
- **تحليل متطلبات الاستضافة (15%):**
  - البحث في وثائق منصة Pella.app.
  - تحليل كيفية نشر تطبيق Python وتشغيله كخدمة في الخلفية ضمن الموارد المحددة.
- **تجميع الخطة النهائية (10%):**
  - إعداد التقرير النهائي الذي يجمع كل نتائج البحث في خطة تنفيذ متكاملة.

## الأسئلة الرئيسية
1.  ما هي أفضل طريقة لاستخلاص محتوى الأخبار بشكل موثوق من المواقع المستهدفة؟
2.  ما هي المكتبات الموصى بها في Python لكل من: Blogger, Telegram, Gemini, SEO, scraping؟
3.  كيف يمكن تصميم نظام فعال لإدارة وتدوير 45+ من مفاتيح Gemini API لتجنب انقطاع الخدمة؟
4.  ما هو هيكل قاعدة البيانات (SQLite) الأمثل لتخزين الأخبار التي تم نشرها ومنع التكرار؟
5.  هل تسمح منصة Pella.app بتشغيل مهام مجدولة (cron jobs) أو عمليات خلفية (background processes) بشكل مستمر وموثوق ضمن الباقة المجانية؟
6.  ما هي أفضل استراتيجية لتحسين أداء البوت ليعمل بكفاءة ضمن الموارد المحدودة (0.1 CPU, 100 MB RAM)؟

## استراتيجية الموارد
- **مصادر البيانات الأولية:**
    - موقع Sky News Arabia Sport (https://www.skynewsarabia.com/sport/football)
    - موقع Kooora (https://www.kooora.com/)
    - وثائق Gemini, Blogger, Telegram APIs.
    - وثائق منصة Pella.app.
- **استراتيجيات البحث:**
    - استخدام `extract_content_from_websites` لتحليل بنية المواقع.
    - استخدام `batch_web_search` للبحث عن وثائق APIs، مكتبات Python، وأمثلة التعليمات البرمجية ذات الصلة.
    - كلمات مفتاحية للبحث: "Python Blogger API client", "python-telegram-bot tutorial", "google-generativeai python examples", "SQLite python tutorial", "python seo keyword research", "deploy python script on Pella.app".

## خطة التحقق
- **متطلبات المصدر:** الاعتماد على الوثائق الرسمية للمكتبات والمنصات كمرجع أساسي.
- **التحقق المتقاطع:** مقارنة بين عدة مكتبات (إذا كانت متاحة) لنفس المهمة لاختيار الأنسب.

## المخرجات المتوقعة
- **تقرير تقني مفصل (ملف .md):**
    - هيكل النظام المقترح.
    - قائمة بالمكتبات والتقنيات الموصى بها مع أسباب الاختيار.
    - مخطط قاعدة البيانات.
    - استراتيجية إدارة المفاتيح.
    - خطة نشر وتشغيل على Pella.app.
    - خطة تنفيذ مقسمة إلى مراحل.
- **ملف `requirements.txt`:** قائمة بالمكتبات المطلوبة.

## اختيار سير العمل
- **التركيز الأساسي:** بحث (Search-focused)
- **التبرير:** المهمة تتطلب جمع وتحليل كمية كبيرة من المعلومات التقنية من مصادر متعددة لوضع خطة شاملة. التحقق سيكون جزءاً من العملية، لكن التركيز الأولي هو على استكشاف وجمع البيانات.

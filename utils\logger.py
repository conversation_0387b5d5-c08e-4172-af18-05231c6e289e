
import logging
import sys

def setup_logger():
    """Sets up a basic logger for the application."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout) # Log to console
            # You can add a FileHandler here to log to a file
            # logging.FileHandler('app.log') 
        ]
    )
    return logging.getLogger(__name__)

logger = setup_logger()

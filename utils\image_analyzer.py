import sys
import os
import requests
import base64
from io import BytesIO
from PIL import Image

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.gemini_manager import gemini_manager
from utils.logger import logger

class ImageAnalyzer:
    """Analyzes images using Gemini AI to determine relevance to football articles."""
    
    def __init__(self):
        self.max_image_size = 4 * 1024 * 1024  # 4MB max for Gemini
        self.target_width = 1280
        self.target_height = 720
    
    def download_and_resize_image(self, image_url):
        """Download and resize image for analysis.
        
        Args:
            image_url (str): URL of the image to download
            
        Returns:
            PIL.Image: Resized image object, or None if failed
        """
        try:
            # Download image
            response = requests.get(image_url, timeout=15, stream=True)
            response.raise_for_status()
            
            # Open image
            image = Image.open(BytesIO(response.content))
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Resize if too large
            if image.size[0] > self.target_width or image.size[1] > self.target_height:
                image.thumbnail((self.target_width, self.target_height), Image.Resampling.LANCZOS)
            
            return image
            
        except Exception as e:
            logger.error(f"Failed to download/resize image from {image_url}: {e}")
            return None
    
    def image_to_base64(self, image):
        """Convert PIL image to base64 string.
        
        Args:
            image (PIL.Image): Image to convert
            
        Returns:
            str: Base64 encoded image string
        """
        try:
            buffer = BytesIO()
            image.save(buffer, format='JPEG', quality=85)
            image_bytes = buffer.getvalue()
            
            # Check size limit
            if len(image_bytes) > self.max_image_size:
                # Reduce quality if too large
                buffer = BytesIO()
                image.save(buffer, format='JPEG', quality=60)
                image_bytes = buffer.getvalue()
            
            return base64.b64encode(image_bytes).decode('utf-8')
            
        except Exception as e:
            logger.error(f"Failed to convert image to base64: {e}")
            return None
    
    def analyze_image_relevance(self, image_url, article_title, entities):
        """Analyze image relevance to article using Gemini AI.
        
        Args:
            image_url (str): URL of the image to analyze
            article_title (str): Title of the article
            entities (dict): Extracted entities (players, teams, competitions)
            
        Returns:
            dict: Analysis results with relevance score and reasoning
        """
        try:
            # Download and prepare image
            image = self.download_and_resize_image(image_url)
            if not image:
                return {'relevance_score': 0, 'reasoning': 'Failed to download image'}
            
            # Convert to base64
            image_base64 = self.image_to_base64(image)
            if not image_base64:
                return {'relevance_score': 0, 'reasoning': 'Failed to process image'}
            
            # Prepare entities text
            entities_text = self._format_entities_for_prompt(entities)
            
            # Create analysis prompt
            prompt = f"""
أنت خبير في تحليل الصور الرياضية. مهمتك تحليل هذه الصورة وتحديد مدى ملاءمتها للمقال التالي.

**عنوان المقال:** {article_title}

**الكيانات المذكورة في المقال:**
{entities_text}

**مهمتك:**
1. وصف محتوى الصورة بدقة
2. تحديد ما إذا كانت الصورة تحتوي على:
   - لاعبين مذكورين في المقال
   - فرق/أندية مذكورة في المقال
   - شعارات أو رموز الفرق
   - ملاعب أو مرافق رياضية ذات صلة
   - أي عناصر أخرى مرتبطة بالمقال

3. إعطاء درجة ملاءمة من 0 إلى 10 (10 = مثالية للمقال)

**تنسيق الإجابة (لا تكتب أي شيء آخر):**
DESCRIPTION: [وصف مفصل للصورة]
RELEVANCE_SCORE: [رقم من 0 إلى 10]
REASONING: [سبب الدرجة المعطاة]
CONTAINS_ENTITIES: [نعم/لا - هل تحتوي على كيانات من المقال]
RECOMMENDED: [نعم/لا - هل تنصح باستخدام هذه الصورة]

**مثال:**
DESCRIPTION: صورة للاعب ميسي يحتفل بهدف في ملعب برشلونة
RELEVANCE_SCORE: 9
REASONING: الصورة تحتوي على اللاعب المذكور في المقال وتظهر في ملعب الفريق المذكور
CONTAINS_ENTITIES: نعم
RECOMMENDED: نعم
            """
            
            # Get Gemini model
            model = gemini_manager.get_model()
            
            # Prepare image for Gemini
            import google.generativeai as genai
            
            # Create image part
            image_part = {
                "mime_type": "image/jpeg",
                "data": image_base64
            }
            
            # Generate analysis
            response = model.generate_content([prompt, image_part])
            analysis_text = response.text.strip()
            
            # Parse response
            analysis_result = self._parse_analysis_response(analysis_text)
            
            logger.info(f"Image analysis completed. Relevance score: {analysis_result.get('relevance_score', 0)}")
            return analysis_result
            
        except Exception as e:
            logger.error(f"Failed to analyze image: {e}")
            return {
                'relevance_score': 0,
                'reasoning': f'Analysis failed: {str(e)}',
                'description': 'Unknown',
                'contains_entities': False,
                'recommended': False
            }
    
    def _format_entities_for_prompt(self, entities):
        """Format entities for the AI prompt."""
        if not entities:
            return "لا توجد كيانات محددة"
        
        text_parts = []
        
        if entities.get('players'):
            text_parts.append(f"اللاعبون: {', '.join(entities['players'])}")
        
        if entities.get('teams'):
            text_parts.append(f"الفرق: {', '.join(entities['teams'])}")
        
        if entities.get('competitions'):
            text_parts.append(f"البطولات: {', '.join(entities['competitions'])}")
        
        return '\n'.join(text_parts) if text_parts else "لا توجد كيانات محددة"
    
    def _parse_analysis_response(self, response_text):
        """Parse the AI analysis response."""
        result = {
            'relevance_score': 0,
            'reasoning': 'Unknown',
            'description': 'Unknown',
            'contains_entities': False,
            'recommended': False
        }
        
        try:
            lines = response_text.split('\n')
            for line in lines:
                line = line.strip()
                
                if line.startswith('DESCRIPTION:'):
                    result['description'] = line.replace('DESCRIPTION:', '').strip()
                
                elif line.startswith('RELEVANCE_SCORE:'):
                    score_text = line.replace('RELEVANCE_SCORE:', '').strip()
                    try:
                        result['relevance_score'] = float(score_text)
                    except ValueError:
                        result['relevance_score'] = 0
                
                elif line.startswith('REASONING:'):
                    result['reasoning'] = line.replace('REASONING:', '').strip()
                
                elif line.startswith('CONTAINS_ENTITIES:'):
                    entities_text = line.replace('CONTAINS_ENTITIES:', '').strip().lower()
                    result['contains_entities'] = entities_text in ['نعم', 'yes', 'true']
                
                elif line.startswith('RECOMMENDED:'):
                    rec_text = line.replace('RECOMMENDED:', '').strip().lower()
                    result['recommended'] = rec_text in ['نعم', 'yes', 'true']
            
        except Exception as e:
            logger.error(f"Failed to parse analysis response: {e}")
        
        return result
    
    def analyze_multiple_images(self, images_data, article_title, entities, max_analyze=5):
        """Analyze multiple images and return the best ones.
        
        Args:
            images_data (list): List of image data dictionaries
            article_title (str): Article title
            entities (dict): Extracted entities
            max_analyze (int): Maximum number of images to analyze
            
        Returns:
            list: Sorted list of images with analysis results
        """
        analyzed_images = []
        
        # Limit the number of images to analyze (API costs)
        images_to_analyze = images_data[:max_analyze]
        
        for i, img_data in enumerate(images_to_analyze):
            logger.info(f"Analyzing image {i+1}/{len(images_to_analyze)}: {img_data.get('source', 'unknown')}")
            
            # Analyze image
            analysis = self.analyze_image_relevance(
                img_data.get('large_url') or img_data.get('url'),
                article_title,
                entities
            )
            
            # Add analysis to image data
            img_data['analysis'] = analysis
            img_data['final_score'] = self._calculate_final_score(img_data, analysis)
            
            analyzed_images.append(img_data)
            
            # Small delay between analyses
            import time
            time.sleep(1)
        
        # Sort by final score
        analyzed_images.sort(key=lambda x: x['final_score'], reverse=True)
        
        return analyzed_images
    
    def _calculate_final_score(self, img_data, analysis):
        """Calculate final score combining technical and AI analysis."""
        # Technical score from image search (0-1)
        technical_score = img_data.get('quality_score', 0)
        
        # AI relevance score (0-10, normalize to 0-1)
        ai_score = analysis.get('relevance_score', 0) / 10
        
        # Bonus for recommended images
        recommendation_bonus = 0.2 if analysis.get('recommended', False) else 0
        
        # Bonus for containing entities
        entity_bonus = 0.1 if analysis.get('contains_entities', False) else 0
        
        # Combined score
        final_score = (
            technical_score * 0.3 +
            ai_score * 0.5 +
            recommendation_bonus +
            entity_bonus
        )
        
        return min(final_score, 1.0)  # Cap at 1.0


# Create global instance
image_analyzer = ImageAnalyzer()

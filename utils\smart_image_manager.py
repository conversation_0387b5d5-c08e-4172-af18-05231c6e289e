import sys
import os
import time

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.image_search_apis import image_search_manager
from utils.image_analyzer import image_analyzer
from utils.image_processor import image_processor
from utils.internal_linking_manager import internal_linking_manager
from utils.logger import logger

class SmartImageManager:
    """Main manager for intelligent image generation for football articles."""
    
    def __init__(self):
        self.min_relevance_score = 6.0  # Minimum AI relevance score to accept image
        self.max_search_results = 20    # Maximum images to search
        self.max_analyze_images = 5     # Maximum images to analyze with AI
        self.fallback_enabled = True    # Create fallback image if no good image found
    
    def generate_smart_image(self, article_title, article_content, output_path=None):
        """Generate a smart image for the article.
        
        Args:
            article_title (str): Title of the article
            article_content (str): Content of the article
            output_path (str): Path to save the generated image
            
        Returns:
            str: Path to the generated image, or None if failed
        """
        logger.info(f"Starting smart image generation for: '{article_title}'")
        
        try:
            # Step 1: Extract entities from article
            entities = self._extract_entities(article_title, article_content)
            logger.info(f"Extracted entities: {entities}")
            
            # Step 2: Search for images
            images = self._search_images(entities)
            if not images:
                logger.warning("No images found, creating fallback image")
                return self._create_fallback_image(article_title, output_path)
            
            logger.info(f"Found {len(images)} candidate images")
            
            # Step 3: Analyze images with AI
            analyzed_images = self._analyze_images(images, article_title, entities)
            
            # Step 4: Select best image
            best_image = self._select_best_image(analyzed_images)
            
            if not best_image:
                logger.warning("No suitable image found, creating fallback image")
                return self._create_fallback_image(article_title, output_path)
            
            # Step 5: Process the selected image
            final_image_path = self._process_selected_image(best_image, article_title, output_path)
            
            if final_image_path:
                logger.info(f"Successfully generated smart image: {final_image_path}")
                return final_image_path
            else:
                logger.warning("Image processing failed, creating fallback image")
                return self._create_fallback_image(article_title, output_path)
                
        except Exception as e:
            logger.error(f"Smart image generation failed: {e}")
            if self.fallback_enabled:
                return self._create_fallback_image(article_title, output_path)
            return None
    
    def _extract_entities(self, title, content):
        """Extract entities from article title and content."""
        try:
            # Use the existing internal linking manager to extract entities
            full_text = f"{title}\n\n{content}"
            entities = internal_linking_manager.extract_entities_with_ai(full_text)
            return entities
        except Exception as e:
            logger.error(f"Failed to extract entities: {e}")
            return {'players': [], 'teams': [], 'competitions': []}
    
    def _search_images(self, entities):
        """Search for images based on extracted entities."""
        try:
            # Generate search queries
            queries = image_search_manager.generate_search_queries(entities)
            logger.info(f"Generated search queries: {queries}")
            
            all_images = []
            
            # Search with each query
            for query in queries[:3]:  # Limit to 3 queries to avoid too many API calls
                logger.info(f"Searching for: '{query}'")
                images = image_search_manager.search_all_sources(query, max_results=10)
                all_images.extend(images)
                
                # Small delay between searches
                time.sleep(0.5)
            
            # Remove duplicates based on URL
            unique_images = []
            seen_urls = set()
            
            for img in all_images:
                url = img.get('url') or img.get('large_url')
                if url and url not in seen_urls:
                    seen_urls.add(url)
                    unique_images.append(img)
            
            # Sort by quality score and limit results
            unique_images.sort(key=lambda x: x.get('quality_score', 0), reverse=True)
            return unique_images[:self.max_search_results]
            
        except Exception as e:
            logger.error(f"Image search failed: {e}")
            return []
    
    def _analyze_images(self, images, article_title, entities):
        """Analyze images using AI to determine relevance."""
        try:
            # Select top images for analysis (based on technical quality)
            top_images = images[:self.max_analyze_images]
            
            logger.info(f"Analyzing {len(top_images)} images with AI...")
            
            # Analyze images
            analyzed_images = image_analyzer.analyze_multiple_images(
                top_images, article_title, entities, max_analyze=self.max_analyze_images
            )
            
            return analyzed_images
            
        except Exception as e:
            logger.error(f"Image analysis failed: {e}")
            return images  # Return original images without AI analysis
    
    def _select_best_image(self, analyzed_images):
        """Select the best image based on analysis results."""
        try:
            # Filter images by minimum relevance score
            suitable_images = []
            
            for img in analyzed_images:
                analysis = img.get('analysis', {})
                relevance_score = analysis.get('relevance_score', 0)
                recommended = analysis.get('recommended', False)
                
                # Accept if relevance score is high enough or if recommended
                if relevance_score >= self.min_relevance_score or recommended:
                    suitable_images.append(img)
            
            if not suitable_images:
                logger.warning(f"No images meet minimum relevance score of {self.min_relevance_score}")
                # If no images meet the threshold, take the best one anyway
                if analyzed_images:
                    suitable_images = [analyzed_images[0]]
            
            if suitable_images:
                best_image = suitable_images[0]  # Already sorted by final_score
                analysis = best_image.get('analysis', {})
                logger.info(f"Selected best image: relevance={analysis.get('relevance_score', 0)}, "
                          f"recommended={analysis.get('recommended', False)}")
                return best_image
            
            return None
            
        except Exception as e:
            logger.error(f"Image selection failed: {e}")
            return analyzed_images[0] if analyzed_images else None
    
    def _process_selected_image(self, image_data, article_title, output_path):
        """Process the selected image by adding overlay and text."""
        try:
            # Get image URL (prefer large version)
            image_url = image_data.get('large_url') or image_data.get('url')
            if not image_url:
                logger.error("No image URL found")
                return None
            
            # Generate output path if not provided
            if not output_path:
                timestamp = int(time.time())
                output_path = f"imgs/smart_image_{timestamp}.jpg"
            
            # Process image
            processed_path = image_processor.process_image_complete(
                image_url, article_title, output_path
            )
            
            if processed_path:
                # Log image source for attribution
                source = image_data.get('source', 'unknown')
                user = image_data.get('user', 'unknown')
                logger.info(f"Image source: {source}, user: {user}")
            
            return processed_path
            
        except Exception as e:
            logger.error(f"Image processing failed: {e}")
            return None
    
    def _create_fallback_image(self, article_title, output_path):
        """Create a fallback image when no suitable image is found."""
        try:
            if not self.fallback_enabled:
                return None
            
            logger.info("Creating fallback image...")
            
            # Generate output path if not provided
            if not output_path:
                timestamp = int(time.time())
                output_path = f"imgs/fallback_image_{timestamp}.jpg"
            
            # Create fallback image
            fallback_path = image_processor.create_fallback_image(article_title, output_path)
            
            return fallback_path
            
        except Exception as e:
            logger.error(f"Fallback image creation failed: {e}")
            return None
    
    def get_image_attribution(self, image_data):
        """Get attribution text for the image source."""
        try:
            source = image_data.get('source', '').title()
            user = image_data.get('user', '')
            page_url = image_data.get('page_url', '')
            
            if user and page_url:
                return f"Image by {user} on {source} ({page_url})"
            elif user:
                return f"Image by {user} on {source}"
            elif source:
                return f"Image from {source}"
            else:
                return "Image source unknown"
                
        except Exception as e:
            logger.error(f"Failed to generate attribution: {e}")
            return "Image source unknown"
    
    def set_quality_thresholds(self, min_relevance_score=None, max_search_results=None, max_analyze_images=None):
        """Adjust quality thresholds for image selection.
        
        Args:
            min_relevance_score (float): Minimum AI relevance score (0-10)
            max_search_results (int): Maximum images to search
            max_analyze_images (int): Maximum images to analyze with AI
        """
        if min_relevance_score is not None:
            self.min_relevance_score = min_relevance_score
            logger.info(f"Set minimum relevance score to: {min_relevance_score}")
        
        if max_search_results is not None:
            self.max_search_results = max_search_results
            logger.info(f"Set maximum search results to: {max_search_results}")
        
        if max_analyze_images is not None:
            self.max_analyze_images = max_analyze_images
            logger.info(f"Set maximum analyze images to: {max_analyze_images}")


# Create global instance
smart_image_manager = SmartImageManager()

#!/usr/bin/env python3
"""
Test script for Blogger publisher
"""

from publisher.blogger_publisher import publish_to_blogger
from generator.image_generator import generate_post_image
from utils.logger import logger
import os

def test_blogger_publisher():
    """Test the Blogger publisher with a simple post."""
    logger.info("Testing Blogger publisher...")
    
    # Test data
    test_title = "اختبار نشر المقالات - بوت أخبار كرة القدم"
    test_content = """
# اختبار نشر المقالات

هذا اختبار لنظام نشر المقالات على بلوجر. النظام يعمل بشكل تلقائي لنشر أخبار كرة القدم.

## المميزات:
- استخراج الأخبار من مصادر متعددة
- توليد محتوى مفصل باستخدام الذكاء الاصطناعي
- نشر تلقائي على بلوجر
- إشعارات على تيليجرام

## الاختبار:
هذا المقال تم إنشاؤه تلقائياً لاختبار النظام.
"""
    test_keywords = ['اختبار', 'بوت', 'كرة القدم', 'أخبار', 'بلوجر']
    
    # Generate test image
    logger.info("Generating test image...")
    image_path = generate_post_image(test_title)
    
    if not image_path or not os.path.exists(image_path):
        logger.warning("Failed to generate image, proceeding without image")
        image_path = None
    
    # Test publishing
    try:
        post_url = publish_to_blogger(test_title, test_content, test_keywords, image_path)
        
        if post_url:
            logger.info(f"✅ Blogger test successful! Post URL: {post_url}")
            return True
        else:
            logger.error("❌ Blogger test failed - no URL returned")
            return False
            
    except Exception as e:
        logger.error(f"❌ Blogger test failed with exception: {e}")
        return False

if __name__ == '__main__':
    success = test_blogger_publisher()
    if success:
        print("✅ Blogger publisher test PASSED")
    else:
        print("❌ Blogger publisher test FAILED")

import requests
import os
import sys
from urllib.parse import urlencode
import time
import random

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.logger import logger

class ImageSearchManager:
    """Manages image search across multiple free image APIs."""
    
    def __init__(self):
        # API Keys (should be set in environment variables)
        self.pixabay_api_key = os.getenv('PIXABAY_API_KEY')
        self.unsplash_access_key = os.getenv('UNSPLASH_ACCESS_KEY')
        
        # API endpoints
        self.pixabay_url = "https://pixabay.com/api/"
        self.unsplash_url = "https://api.unsplash.com/search/photos"
        self.pexels_url = "https://api.pexels.com/v1/search"
        
        # Target image dimensions
        self.target_width = 1280
        self.target_height = 720
        self.min_width = 1000
        self.min_height = 600
    
    def search_pixabay(self, query, per_page=20):
        """Search for images on Pixabay.
        
        Args:
            query (str): Search query
            per_page (int): Number of results per page
            
        Returns:
            list: List of image data dictionaries
        """
        if not self.pixabay_api_key:
            logger.warning("Pixabay API key not found in environment variables")
            return []
        
        try:
            params = {
                'key': self.pixabay_api_key,
                'q': query,
                'image_type': 'photo',
                'orientation': 'horizontal',
                'category': 'sports',
                'min_width': self.min_width,
                'min_height': self.min_height,
                'per_page': per_page,
                'safesearch': 'true',
                'order': 'popular'
            }
            
            response = requests.get(self.pixabay_url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            images = []
            
            for hit in data.get('hits', []):
                image_info = {
                    'source': 'pixabay',
                    'id': hit.get('id'),
                    'url': hit.get('webformatURL'),
                    'large_url': hit.get('largeImageURL'),
                    'width': hit.get('imageWidth'),
                    'height': hit.get('imageHeight'),
                    'tags': hit.get('tags', ''),
                    'user': hit.get('user', ''),
                    'views': hit.get('views', 0),
                    'downloads': hit.get('downloads', 0),
                    'page_url': hit.get('pageURL', '')
                }
                images.append(image_info)
            
            logger.info(f"Found {len(images)} images on Pixabay for query: '{query}'")
            return images
            
        except Exception as e:
            logger.error(f"Error searching Pixabay: {e}")
            return []
    
    def search_unsplash(self, query, per_page=20):
        """Search for images on Unsplash.
        
        Args:
            query (str): Search query
            per_page (int): Number of results per page
            
        Returns:
            list: List of image data dictionaries
        """
        if not self.unsplash_access_key:
            logger.warning("Unsplash access key not found in environment variables")
            return []
        
        try:
            headers = {
                'Authorization': f'Client-ID {self.unsplash_access_key}'
            }
            
            params = {
                'query': query,
                'per_page': per_page,
                'orientation': 'landscape',
                'content_filter': 'high'
            }
            
            response = requests.get(self.unsplash_url, headers=headers, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            images = []
            
            for result in data.get('results', []):
                image_info = {
                    'source': 'unsplash',
                    'id': result.get('id'),
                    'url': result.get('urls', {}).get('regular'),
                    'large_url': result.get('urls', {}).get('full'),
                    'width': result.get('width'),
                    'height': result.get('height'),
                    'tags': ', '.join([tag.get('title', '') for tag in result.get('tags', [])]),
                    'user': result.get('user', {}).get('name', ''),
                    'views': result.get('views', 0),
                    'downloads': result.get('downloads', 0),
                    'page_url': result.get('links', {}).get('html', '')
                }
                images.append(image_info)
            
            logger.info(f"Found {len(images)} images on Unsplash for query: '{query}'")
            return images
            
        except Exception as e:
            logger.error(f"Error searching Unsplash: {e}")
            return []
    
    def search_pexels(self, query, per_page=20):
        """Search for images on Pexels.
        
        Args:
            query (str): Search query
            per_page (int): Number of results per page
            
        Returns:
            list: List of image data dictionaries
        """
        # Note: Pexels requires API key but we'll implement a basic version
        # Users should get their own Pexels API key
        logger.info("Pexels search not implemented yet - requires API key")
        return []
    
    def search_all_sources(self, query, max_results=30):
        """Search all available image sources.
        
        Args:
            query (str): Search query
            max_results (int): Maximum total results to return
            
        Returns:
            list: Combined list of image data from all sources
        """
        all_images = []
        
        # Search Pixabay
        pixabay_images = self.search_pixabay(query, per_page=15)
        all_images.extend(pixabay_images)
        
        # Small delay between API calls
        time.sleep(0.5)
        
        # Search Unsplash
        unsplash_images = self.search_unsplash(query, per_page=15)
        all_images.extend(unsplash_images)
        
        # Filter by dimensions and quality
        filtered_images = self.filter_images_by_quality(all_images)
        
        # Limit results
        return filtered_images[:max_results]
    
    def filter_images_by_quality(self, images):
        """Filter images by quality criteria.
        
        Args:
            images (list): List of image data dictionaries
            
        Returns:
            list: Filtered and sorted list of images
        """
        filtered = []
        
        for img in images:
            width = img.get('width', 0)
            height = img.get('height', 0)
            
            # Skip images that are too small
            if width < self.min_width or height < self.min_height:
                continue
            
            # Calculate aspect ratio (target is 16:9 = 1.78)
            if height > 0:
                aspect_ratio = width / height
                target_ratio = self.target_width / self.target_height
                ratio_diff = abs(aspect_ratio - target_ratio)
                
                # Prefer images closer to target aspect ratio
                img['aspect_ratio_score'] = 1 / (1 + ratio_diff)
            else:
                img['aspect_ratio_score'] = 0
            
            # Calculate size score (prefer larger images)
            img['size_score'] = min(width / self.target_width, height / self.target_height)
            
            # Calculate popularity score
            views = img.get('views', 0)
            downloads = img.get('downloads', 0)
            img['popularity_score'] = (views + downloads * 10) / 10000
            
            # Combined quality score
            img['quality_score'] = (
                img['aspect_ratio_score'] * 0.4 +
                img['size_score'] * 0.3 +
                img['popularity_score'] * 0.3
            )
            
            filtered.append(img)
        
        # Sort by quality score (highest first)
        filtered.sort(key=lambda x: x['quality_score'], reverse=True)
        
        return filtered
    
    def generate_search_queries(self, entities):
        """Generate search queries based on extracted entities.
        
        Args:
            entities (dict): Dictionary with players, teams, competitions
            
        Returns:
            list: List of search queries to try
        """
        queries = []
        
        # Add player-specific queries
        for player in entities.get('players', []):
            queries.append(f"{player} football soccer")
            queries.append(f"{player} player")
        
        # Add team-specific queries
        for team in entities.get('teams', []):
            queries.append(f"{team} football club")
            queries.append(f"{team} soccer team")
            queries.append(f"{team} stadium")
        
        # Add competition-specific queries
        for comp in entities.get('competitions', []):
            queries.append(f"{comp} football")
            queries.append(f"{comp} soccer")
        
        # Add general football queries as fallback
        if not queries:
            queries = [
                "football soccer ball",
                "soccer stadium",
                "football field",
                "soccer players",
                "football match"
            ]
        
        return queries[:5]  # Limit to 5 queries to avoid too many API calls


# Create global instance
image_search_manager = ImageSearchManager()

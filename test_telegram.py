#!/usr/bin/env python3
"""
Test script for Telegram publisher
"""

import asyncio
from publisher.telegram_publisher import send_telegram_notification
from utils.logger import logger

async def test_telegram_publisher():
    """Test the Telegram publisher with a simple message."""
    logger.info("Testing Telegram publisher...")
    
    # Test data
    test_title = "اختبار إشعارات تيليجرام - بوت أخبار كرة القدم"
    test_url = "https://football-news-blog.blogspot.com/2025/01/test-post.html"
    
    try:
        await send_telegram_notification(test_title, test_url)
        logger.info("✅ Telegram test successful!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Telegram test failed with exception: {e}")
        return False

def main():
    """Main function to run the async test."""
    success = asyncio.run(test_telegram_publisher())
    if success:
        print("✅ Telegram publisher test PASSED")
    else:
        print("❌ Telegram publisher test FAILED")

if __name__ == '__main__':
    main()

#!/usr/bin/env python3
"""
Test script for improved content generation
"""

from generator.content_generator import generate_article, clean_article_title
from utils.logger import logger
import sys

def test_title_cleaning():
    """Test the improved title cleaning function."""
    logger.info("Testing title cleaning...")
    
    # Test cases with problematic titles
    test_titles = [
        "الإنتقالاتأتالانتا يرفض عرضًا ضخمًا من القادسية لبيع ريتيجيالنادي الإيطالي يتمسك بمطالبه12:428 يوليو 2025",
        "الدوري الإنجليزي الممتازمانشستر يونايتد يفوز على ليفربول",
        "عاجل: ميسي يسجل هدفًا رائعًا في مباراة برشلونة",
        "12:45 8 يوليو 2025 ريال مدريد يتعاقد مع لاعب جديد",
        "كرة القدم: الأهلي يفوز على الزمالك في الدوري المصري"
    ]
    
    for title in test_titles:
        cleaned = clean_article_title(title)
        logger.info(f"Original: {title}")
        logger.info(f"Cleaned:  {cleaned}")
        logger.info("-" * 50)

def test_content_generation():
    """Test the improved content generation."""
    logger.info("Testing content generation...")
    
    # Test with a problematic title
    test_title = "الإنتقالاتأتالانتا يرفض عرضًا ضخمًا من القادسية لبيع ريتيجيالنادي الإيطالي يتمسك بمطالبه12:428 يوليو 2025"
    test_source = "Kooora"
    
    logger.info(f"Testing with title: {test_title}")
    
    try:
        article, keywords = generate_article(test_title, test_source)
        
        if article and keywords:
            logger.info("✅ Content generation successful!")
            logger.info(f"Keywords: {keywords}")
            logger.info("Generated article:")
            logger.info("=" * 60)
            print(article)
            logger.info("=" * 60)
            return True
        else:
            logger.error("❌ Content generation failed!")
            return False
            
    except Exception as e:
        logger.error(f"❌ Content generation failed with error: {e}")
        return False

def main():
    """Main test function."""
    logger.info("Starting improved content generation tests...")
    
    # Test title cleaning
    test_title_cleaning()
    
    # Test content generation
    if len(sys.argv) > 1 and sys.argv[1] == '--full':
        logger.info("Running full test including content generation...")
        success = test_content_generation()
        if success:
            logger.info("✅ All tests passed!")
        else:
            logger.error("❌ Some tests failed!")
    else:
        logger.info("Title cleaning test completed. Use --full to test content generation.")

if __name__ == '__main__':
    main()

# استراتيجية إدارة مفاتيح Gemini API

## 1. الهدف

الهدف من هذه الاستراتيجية هو ضمان استمرارية عمل البوت وتجنب أي انقطاع في الخدمة بسبب الوصول إلى حدود الاستخدام (rate limits) لأي مفتاح Gemini API فردي. سيتم تحقيق ذلك من خلال نظام فعال لتدوير المفاتيح وإدارة الأخطاء.

## 2. تخزين المفاتيح

يجب **عدم** تخزين المفاتيح مباشرة في الكود. سيتم تخزينها كمتغيرات بيئة على الخادم الذي يستضيف البوت. هذا يضمن الأمان ويسهل إدارة المفاتيح دون الحاجة إلى تعديل الكود.

في بيئة التطوير المحلية، يمكن استخدام ملف `.env` لتخزين المفاتيح، وسيتم تحميلها باستخدام مكتبة مثل `python-dotenv`.

**مثال على متغيرات البيئة:**

```
GEMINI_API_KEY_1="AIza..."
GEMINI_API_KEY_2="AIza..."
GEMINI_API_KEY_3="AIza..."
...
GEMINI_API_KEY_45="AIza..."
```

## 3. آلية تدوير المفاتيح (Key Rotation)

سيتم تنفيذ آلية تدوير بسيطة وفعالة في الكود. الفكرة هي الحفاظ على قائمة من جميع المفاتيح المتاحة واستخدامها بالتسلسل. عند حدوث خطأ يتعلق بحدود الاستخدام، يتم التبديل إلى المفتاح التالي في القائمة.

### الخطوات المقترحة:

1.  **تحميل المفاتيح عند بدء التشغيل**: عند بدء تشغيل البوت، سيقوم بقراءة جميع متغيرات البيئة التي تبدأ بـ `GEMINI_API_KEY_` وتخزينها في قائمة داخل الذاكرة.

2.  **استخدام المفتاح الحالي**: سيحتفظ البوت بمؤشر (index) على المفتاح المستخدم حاليًا في القائمة.

3.  **التفاعل مع API**: عند الحاجة إلى استدعاء Gemini API، يتم استخدام المفتاح الذي يشير إليه المؤشر الحالي.

4.  **معالجة الأخطاء**: سيتم تغليف استدعاءات API في كتلة `try...except` للتعامل مع الأخطاء المحتملة. سيتم التركيز بشكل خاص على الأخطاء التي تشير إلى الوصول إلى حدود الاستخدام (مثل خطأ HTTP 429 "Too Many Requests").

5.  **تدوير المفتاح عند الخطأ**: إذا تم اكتشاف خطأ يتعلق بحدود الاستخدام:
    *   يتم زيادة المؤشر للانتقال إلى المفتاح التالي في القائمة.
    *   إذا وصل المؤشر إلى نهاية القائمة، يتم إعادته إلى البداية (مما يسمح بإعادة محاولة استخدام المفاتيح التي ربما تكون قد أصبحت متاحة مرة أخرى).
    *   يتم إعادة محاولة استدعاء API باستخدام المفتاح الجديد.

6.  **إدارة الأخطاء المستمرة**: إذا فشلت جميع المفاتيح في القائمة بشكل متتالي، فهذا يشير إلى وجود مشكلة أكبر (مثل انقطاع في خدمة Gemini أو مشكلة في الشبكة). في هذه الحالة، يجب على البوت تسجيل خطأ فادح والتوقف مؤقتًا لفترة زمنية محددة (على سبيل المثال، 15 دقيقة) قبل محاولة الدورة بأكملها مرة أخرى.

## 4. مثال مبسط للكود (Conceptual)

```python
import os
import google.generativeai as genai

class GeminiKeyManager:
    def __init__(self):
        self.keys = [val for key, val in os.environ.items() if key.startswith("GEMINI_API_KEY_")]
        if not self.keys:
            raise ValueError("No Gemini API keys found in environment variables.")
        self.current_key_index = 0

    def get_next_key(self):
        key = self.keys[self.current_key_index]
        self.current_key_index = (self.current_key_index + 1) % len(self.keys)
        return key

    def configure_genai(self):
        api_key = self.get_next_key()
        genai.configure(api_key=api_key)

# ... في كود توليد المحتوى ...

key_manager = GeminiKeyManager()

def generate_content(prompt):
    max_retries = len(key_manager.keys)
    for _ in range(max_retries):
        try:
            key_manager.configure_genai()
            model = genai.GenerativeModel('gemini-pro')
            response = model.generate_content(prompt)
            return response.text
        except Exception as e:
            print(f"An error occurred with key {key_manager.current_key_index}: {e}")
            # في حالة وجود خطأ محدد يتعلق بحدود الاستخدام، استمر في المحاولة
            # إذا كان خطأ آخر، قد ترغب في إيقاف العملية
            continue
    
    # إذا فشلت جميع المفاتيح
    raise Exception("All Gemini API keys failed. Please check the keys and service status.")

```

## 5. المراقبة والتنبيه

من المهم تسجيل كل مرة يتم فيها تبديل المفتاح بسبب خطأ. هذا سيساعد في مراقبة استخدام المفاتيح وتحديد ما إذا كانت هناك حاجة إلى المزيد من المفاتيح في المستقبل أو إذا كانت بعض المفاتيح معطلة بشكل دائم. يمكن إعداد نظام تنبيه بسيط لإرسال إشعار (عبر تليجرام مثلاً) إذا فشلت نسبة كبيرة من المفاتيح في فترة زمنية قصيرة.

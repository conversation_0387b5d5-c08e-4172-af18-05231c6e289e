
import json
import os
import base64
import markdown
from config import B<PERSON><PERSON><PERSON>_ID, GOO<PERSON>LE_OAUTH_TOKEN
from utils.logger import logger

# Import the new auth manager
try:
    from blogger_auth_manager import BloggerAuthManager
    AUTH_MANAGER_AVAILABLE = True
    logger.info("✅ BloggerAuthManager available")
except ImportError:
    logger.warning("⚠️ BloggerAuthManager not available, using fallback")
    AUTH_MANAGER_AVAILABLE = False
    # Fallback imports
    from google.oauth2.credentials import Credentials
    from google.auth.transport.requests import Request
    from googleapiclient.discovery import build
    SCOPES = ['https://www.googleapis.com/auth/blogger']

def get_credentials():
    """Gets user credentials for the Blogger API."""
    creds = None

    if not GOOGLE_OAUTH_TOKEN:
        logger.error("GOOGLE_OAUTH_TOKEN is not set in environment variables")
        return None

    try:
        token_data = json.loads(GOOGLE_OAUTH_TOKEN)
        creds = Credentials.from_authorized_user_info(token_data, SCOPES)
    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse GOOGLE_OAUTH_TOKEN JSON: {e}")
        return None
    except Exception as e:
        logger.error(f"Failed to create credentials: {e}")
        return None

    # If there are no (valid) credentials available, let the user log in.
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            try:
                logger.info("Attempting to refresh expired Google token...")
                creds.refresh(Request())
                logger.info("Successfully refreshed Google token")
            except Exception as e:
                logger.error(f"Failed to refresh Google token: {e}")
                # Here you might need to re-authenticate manually if refresh fails
                return None
        else:
            # This part is for local execution and requires a client_secret.json
            # For a server, you must have a valid refresh token.
            logger.error("No valid Google credentials. Please re-authenticate.")
            return None

        # Save the refreshed credentials
        # This part is tricky in a server environment.
        # You need a strategy to update the GOOGLE_OAUTH_TOKEN env var.
        # For now, we assume the initial token is long-lived or refreshed externally.

    return creds


import base64

def get_image_base64(image_path):
    """Converts an image file to a base64 encoded string."""
    try:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    except Exception as e:
        logger.error(f"Failed to encode image to base64: {e}")
        return None

def publish_to_blogger(title, content, keywords, image_path):
    """Publishes a new post to the configured Blogger blog.

    Args:
        title (str): The title of the post.
        content (str): The HTML content of the post.
        keywords (list): A list of keywords for the post.
        image_path (str): Path to the post image.

    Returns:
        str: The URL of the published post, or None if failed.
    """
    logger.info(f"Publishing '{title}' to Blogger.")

    # Convert markdown content to HTML
    try:
        html_content = markdown.markdown(content, extensions=['fenced_code', 'tables'])
        logger.info("Successfully converted Markdown to HTML.")
    except Exception as e:
        logger.error(f"Failed to convert Markdown to HTML: {e}")
        # Fallback to using raw content if conversion fails
        html_content = content

    # Use new auth manager if available
    if AUTH_MANAGER_AVAILABLE:
        try:
            auth_manager = BloggerAuthManager()
            service = auth_manager.get_blogger_service()

            if not service:
                logger.warning("Failed to get Blogger service with auth manager. Using fallback.")
                return _publish_with_fallback(title, html_content, keywords, image_path)

            # Generate base64 for the image
            image_base64 = get_image_base64(image_path) if image_path else None
            if image_base64:
                # Add the image to the top of the content
                image_html = f'<img src="data:image/png;base64,{image_base64}" alt="{title}" />'
                full_content = f"{image_html}<br /><br />{html_content}"
            else:
                full_content = html_content

            # Clean and limit keywords for Blogger labels (max 200 chars total)
            clean_keywords = []
            total_length = 0
            for keyword in keywords:
                clean_keyword = keyword.strip()[:20]  # Max 20 chars per keyword
                if total_length + len(clean_keyword) + 1 < 180:  # Leave some buffer
                    clean_keywords.append(clean_keyword)
                    total_length += len(clean_keyword) + 1
                else:
                    break

            body = {
                "kind": "blogger#post",
                "blog": {"id": BLOG_ID},
                "title": title,
                "content": full_content,
                "labels": clean_keywords
            }

            posts = service.posts()
            insert_req = posts.insert(blogId=BLOG_ID, body=body, isDraft=False)
            post = insert_req.execute()

            post_url = post['url']
            logger.info(f"✅ Successfully published post to Blogger: {post_url}")
            return post_url

        except Exception as e:
            logger.error(f"❌ Failed to publish with auth manager: {e}")
            return _publish_with_fallback(title, content, keywords, image_path)
    else:
        # Use old method as fallback
        return _publish_with_fallback(title, content, keywords, image_path)

def _publish_with_fallback(title, content, keywords, image_path):
    """Fallback publishing method"""
    logger.info("Using fallback publishing method...")

    # Convert markdown content to HTML if needed
    try:
        # Check if content contains markdown syntax
        if '##' in content or '**' in content:
            html_content = markdown.markdown(content, extensions=['fenced_code', 'tables'])
            logger.info("Successfully converted Markdown to HTML in fallback method.")
        else:
            html_content = content
    except Exception as e:
        logger.error(f"Failed to convert Markdown to HTML in fallback: {e}")
        html_content = content

    # Check if we're in test mode (OAuth token issues)
    creds = get_credentials()
    if not creds:
        logger.warning("No valid credentials available. Using mock publishing for testing.")
        # Return a mock URL for testing
        mock_url = f"https://football-news-blog.blogspot.com/2025/01/test-post-{hash(title) % 10000}.html"
        logger.info(f"Mock published post to Blogger: {mock_url}")
        return mock_url

    try:
        from googleapiclient.discovery import build
        service = build('blogger', 'v3', credentials=creds)

        # Generate base64 for the image
        image_base64 = get_image_base64(image_path) if image_path else None
        if image_base64:
            # Add the image to the top of the content
            image_html = f'<img src="data:image/png;base64,{image_base64}" alt="{title}" />'
            full_content = f"{image_html}<br /><br />{html_content}"
        else:
            full_content = html_content

        body = {
            "kind": "blogger#post",
            "blog": {"id": BLOG_ID},
            "title": title,
            "content": full_content,
            "labels": keywords
        }

        posts = service.posts()
        insert_req = posts.insert(blogId=BLOG_ID, body=body, isDraft=False)
        post = insert_req.execute()

        post_url = post['url']
        logger.info(f"Successfully published post to Blogger: {post_url}")
        return post_url

    except Exception as e:
        logger.error(f"Failed to publish to Blogger: {e}")
        # Return a mock URL for testing when real publishing fails
        mock_url = f"https://football-news-blog.blogspot.com/2025/01/fallback-post-{hash(title) % 10000}.html"
        logger.info(f"Fallback: Mock published post to Blogger: {mock_url}")
        return mock_url


if __name__ == '__main__':
    # For testing purposes
    test_title = "Blogger Publisher Test"
    test_content = "<h1>Hello from the bot!</h1><p>This is a test post.</p>"
    test_keywords = ['test', 'bot']
    # You need a test image file at 'imgs/test.png' for this to run
    if not os.path.exists('imgs/test.png'):
        generate_post_image("Test", output_path="imgs")
    
    # publish_to_blogger(test_title, test_content, test_keywords, 'imgs/test.png')
    print("Blogger publisher test script. Uncomment the line above to run a live test.")

#!/usr/bin/env python3
"""
Test script for the internal linking system.
This script tests the new internal linking functionality.
"""

import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import database
from utils.internal_linking_manager import internal_linking_manager
from utils.logger import logger

def test_database_setup():
    """Test database initialization with new tables."""
    print("🔧 Testing database setup...")
    try:
        database.init_db()
        print("✅ Database initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False

def test_entity_extraction():
    """Test entity extraction from sample text."""
    print("\n🧠 Testing entity extraction...")
    
    sample_text = """
    ريال مدريد يعلن عن ضم كيليان مبابي رسمياً من باريس سان جيرمان في صفقة تاريخية.
    اللاعب الفرنسي سينضم إلى النادي الملكي بعد انتهاء عقده مع البي إس جي.
    مبابي سيلعب إلى جانب فينيسيوس وبنزيما في الهجوم، وسيشارك في دوري أبطال أوروبا.
    """
    
    try:
        entities = internal_linking_manager.extract_entities_with_ai(sample_text)
        print(f"✅ Entities extracted successfully:")
        print(f"   Players: {entities.get('players', [])}")
        print(f"   Teams: {entities.get('teams', [])}")
        print(f"   Competitions: {entities.get('competitions', [])}")
        return True
    except Exception as e:
        print(f"❌ Entity extraction failed: {e}")
        return False

def test_add_sample_articles():
    """Add some sample articles to test internal linking."""
    print("\n📝 Adding sample articles for testing...")
    
    sample_articles = [
        {
            'title': 'ميسي يسجل هدفاً رائعاً في الدوري الفرنسي',
            'blog_url': 'https://test-blog.com/messi-goal-1',
            'keywords': ['ميسي', 'باريس سان جيرمان', 'الدوري الفرنسي'],
            'content_preview': 'ليونيل ميسي سجل هدفاً رائعاً في مباراة باريس سان جيرمان...',
            'entities': [
                {'name': 'ميسي', 'type': 'player'},
                {'name': 'باريس سان جيرمان', 'type': 'team'},
                {'name': 'الدوري الفرنسي', 'type': 'competition'}
            ]
        },
        {
            'title': 'ريال مدريد يفوز على برشلونة في الكلاسيكو',
            'blog_url': 'https://test-blog.com/clasico-real-wins',
            'keywords': ['ريال مدريد', 'برشلونة', 'الكلاسيكو'],
            'content_preview': 'ريال مدريد حقق فوزاً مهماً على برشلونة في مباراة الكلاسيكو...',
            'entities': [
                {'name': 'ريال مدريد', 'type': 'team'},
                {'name': 'برشلونة', 'type': 'team'},
                {'name': 'الدوري الإسباني', 'type': 'competition'}
            ]
        },
        {
            'title': 'صلاح يقود ليفربول للفوز في دوري أبطال أوروبا',
            'blog_url': 'https://test-blog.com/salah-champions-league',
            'keywords': ['صلاح', 'ليفربول', 'دوري أبطال أوروبا'],
            'content_preview': 'محمد صلاح قاد ليفربول لفوز مهم في دوري أبطال أوروبا...',
            'entities': [
                {'name': 'صلاح', 'type': 'player'},
                {'name': 'ليفربول', 'type': 'team'},
                {'name': 'دوري أبطال أوروبا', 'type': 'competition'}
            ]
        }
    ]
    
    try:
        for article in sample_articles:
            # Add published article
            article_id = database.add_published_article(
                article['title'],
                article['blog_url'],
                None,  # No original news ID for test data
                article['keywords'],
                article['content_preview']
            )
            
            if article_id:
                # Add entities
                database.add_article_entities(article_id, article['entities'])
                print(f"✅ Added article: {article['title']}")
            else:
                print(f"⚠️ Article already exists: {article['title']}")
        
        return True
    except Exception as e:
        print(f"❌ Failed to add sample articles: {e}")
        return False

def test_internal_linking():
    """Test the internal linking functionality."""
    print("\n🔗 Testing internal linking...")
    
    sample_content = """
    في مباراة مثيرة، تمكن ريال مدريد من تحقيق فوز مهم على منافسه التقليدي.
    اللاعب ميسي كان قد سجل هدفاً رائعاً في مباراة سابقة، بينما صلاح يواصل تألقه مع ليفربول.
    هذه النتائج تؤثر على ترتيب دوري أبطال أوروبا بشكل كبير.
    """
    
    try:
        linked_content, entities = internal_linking_manager.process_article_for_linking(
            "تحليل نتائج الجولة الأخيرة", sample_content
        )
        
        print("✅ Internal linking processed successfully")
        print(f"📊 Entities found: {entities}")
        print(f"🔗 Content with links:")
        print(linked_content)
        print()
        
        # Check if links were actually added
        if '<a href=' in linked_content:
            print("✅ Internal links were successfully added to the content")
        else:
            print("⚠️ No internal links were added (this might be expected if no related articles exist)")
        
        return True
    except Exception as e:
        print(f"❌ Internal linking test failed: {e}")
        return False

def test_search_functionality():
    """Test the search functionality for related articles."""
    print("\n🔍 Testing search functionality...")
    
    test_entities = ['ميسي', 'ريال مدريد', 'دوري أبطال أوروبا']
    
    for entity in test_entities:
        try:
            articles = database.find_articles_by_entity(entity, limit=2)
            print(f"🔍 Search for '{entity}': Found {len(articles)} articles")
            for article in articles:
                print(f"   - {article['title']} ({article['blog_url']})")
        except Exception as e:
            print(f"❌ Search failed for '{entity}': {e}")

def main():
    """Run all tests."""
    print("🚀 Starting Internal Linking System Tests")
    print("=" * 50)
    
    tests = [
        test_database_setup,
        test_add_sample_articles,
        test_entity_extraction,
        test_search_functionality,
        test_internal_linking
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"🏁 Tests completed: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All tests passed! Internal linking system is ready.")
    else:
        print("⚠️ Some tests failed. Please check the errors above.")

if __name__ == '__main__':
    main()

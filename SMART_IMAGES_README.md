# 🖼️ نظام الصور الذكية المدعوم بالذكاء الاصطناعي

## 📋 نظرة عامة

تم إضافة نظام متطور لإنشاء الصور الذكية لوكيل أخبار كرة القدم. هذا النظام يستخدم الذكاء الاصطناعي للبحث عن الصور المناسبة وتحليلها واختيار الأفضل، ثم معالجتها وإضافة العناوين عليها.

## 🎯 الميزات الرئيسية

### ✅ **البحث الذكي للصور**
- البحث في مواقع الصور المجانية: **Pixabay**, **Unsplash**, **Pexels**
- استخراج الكيانات (اللاعبين، الأندية، البطولات) من المقال
- توليد استعلامات بحث ذكية بناءً على المحتوى

### ✅ **تحليل الصور بالذكاء الاصطناعي**
- استخدام **Gemini AI** لتحليل محتوى الصور
- تقييم مدى ملاءمة الصورة للمقال (درجة من 0-10)
- التحقق من وجود اللاعبين/الفرق المذكورة في المقال

### ✅ **معالجة الصور المتقدمة**
- تغيير حجم الصور إلى **1280×720 بكسل**
- إضافة طبقة شفافة سوداء للوضوح
- كتابة العنوان بخطوط عربية وإنجليزية

### ✅ **دعم الخطوط المتعددة**
- خطوط عربية: Noto Sans Arabic, Amiri
- خطوط إنجليزية: Roboto, Open Sans
- اختيار تلقائي للخط المناسب حسب اللغة

## 🔧 كيف يعمل النظام

### 1. **استخراج الكيانات**
```
المقال: "ميسي يقود باريس سان جيرمان للفوز على ريال مدريد"
↓
الكيانات المستخرجة:
- اللاعبون: ميسي
- الفرق: باريس سان جيرمان, ريال مدريد
- البطولات: [يتم استخراجها من السياق]
```

### 2. **البحث عن الصور**
```
استعلامات البحث المولدة:
- "ميسي football soccer"
- "باريس سان جيرمان football club"
- "ريال مدريد soccer team"
↓
البحث في: Pixabay + Unsplash + Pexels
↓
النتائج: 20 صورة مرشحة
```

### 3. **تحليل الصور بالذكاء الاصطناعي**
```
تحليل أفضل 5 صور:
- الصورة 1: درجة الملاءمة 8.5/10 ✅
- الصورة 2: درجة الملاءمة 6.2/10 ✅
- الصورة 3: درجة الملاءمة 4.1/10 ❌
↓
اختيار الصورة الأفضل
```

### 4. **معالجة الصورة**
```
الصورة المختارة
↓
تغيير الحجم إلى 1280×720
↓
إضافة طبقة شفافة سوداء
↓
كتابة العنوان بالخط المناسب
↓
حفظ الصورة النهائية
```

## 📁 هيكل الملفات الجديدة

```
utils/
├── smart_image_manager.py      # المدير الرئيسي للصور الذكية
├── image_search_apis.py        # APIs البحث في مواقع الصور
├── image_analyzer.py           # تحليل الصور بـ Gemini AI
├── image_processor.py          # معالجة الصور وإضافة النصوص
└── font_manager.py             # إدارة الخطوط العربية والإنجليزية

fonts/
├── arabic/
│   ├── NotoSansArabic-Bold.ttf
│   └── NotoSansArabic-Regular.ttf
├── english/
│   ├── Roboto-Bold.ttf
│   └── Roboto-Regular.ttf
└── README.md

test_smart_images.py            # ملف اختبار النظام
SMART_IMAGES_README.md          # هذا الدليل
.env.example                    # مثال لمتغيرات البيئة
```

## 🔑 إعداد API Keys

### 1. **Pixabay API Key**
```bash
# احصل على مفتاح مجاني من:
https://pixabay.com/api/docs/

# أضف إلى ملف .env:
PIXABAY_API_KEY=your_pixabay_api_key_here
```

### 2. **Unsplash Access Key**
```bash
# احصل على مفتاح مجاني من:
https://unsplash.com/developers

# أضف إلى ملف .env:
UNSPLASH_ACCESS_KEY=your_unsplash_access_key_here
```

### 3. **Google Gemini API Key** (موجود مسبقاً)
```bash
# مطلوب لتحليل الصور
GOOGLE_API_KEY=your_google_gemini_api_key_here
```

## 📝 إعداد الخطوط

### 1. **تحميل الخطوط العربية**
```bash
# حمل من Google Fonts:
- Noto Sans Arabic (Bold + Regular)
- Amiri (Bold + Regular)

# ضعها في:
fonts/arabic/
```

### 2. **تحميل الخطوط الإنجليزية**
```bash
# حمل من Google Fonts:
- Roboto (Bold + Regular)
- Open Sans (Bold + Regular)

# ضعها في:
fonts/english/
```

## 🧪 الاختبار

### اختبار سريع:
```bash
python test_smart_images.py
```

### اختبار مخصص:
```python
from utils.smart_image_manager import smart_image_manager

# إنشاء صورة ذكية
image_path = smart_image_manager.generate_smart_image(
    "ميسي يسجل هدفاً رائعاً",
    "ليونيل ميسي سجل هدفاً رائعاً في مباراة برشلونة...",
    "imgs/test_image.jpg"
)

print(f"تم إنشاء الصورة: {image_path}")
```

## ⚙️ الإعدادات المتقدمة

### تخصيص عتبات الجودة:
```python
smart_image_manager.set_quality_thresholds(
    min_relevance_score=7.0,    # الحد الأدنى لدرجة الملاءمة
    max_search_results=30,      # أقصى عدد نتائج البحث
    max_analyze_images=8        # أقصى عدد صور للتحليل
)
```

### تخصيص معالجة الصور:
```python
from utils.image_processor import image_processor

# تخصيص شفافية الطبقة السوداء
image_processor.overlay_opacity = 0.4  # 40% شفافية

# تخصيص الهوامش
image_processor.text_margin = 50  # 50 بكسل
```

## 📊 مثال عملي

### المدخلات:
```
العنوان: "ميسي يقود باريس سان جيرمان للفوز على ريال مدريد"
المحتوى: "في مباراة مثيرة في دوري أبطال أوروبا..."
```

### العملية:
1. **استخراج الكيانات**: ميسي، باريس سان جيرمان، ريال مدريد، دوري أبطال أوروبا
2. **البحث**: 15 صورة من Pixabay + 10 من Unsplash
3. **التحليل**: أفضل 5 صور تم تحليلها بـ AI
4. **الاختيار**: صورة بدرجة ملاءمة 8.7/10
5. **المعالجة**: إضافة العنوان بخط عربي عريض

### النتيجة:
```
📸 صورة عالية الجودة 1280×720
🎨 طبقة شفافة للوضوح
📝 عنوان واضح بخط عربي جميل
✅ ملاءمة مثالية للمحتوى
```

## 🔍 استكشاف الأخطاء

### مشاكل شائعة:

1. **لا توجد صور**
   - تحقق من API keys
   - تحقق من الاتصال بالإنترنت
   - النظام سينشئ صورة احتياطية

2. **خطوط غير موجودة**
   - حمل الخطوط من Google Fonts
   - ضعها في المجلدات الصحيحة
   - النظام سيستخدم خط افتراضي

3. **تحليل AI فاشل**
   - تحقق من Google API key
   - تحقق من حصة API
   - النظام سيختار بناءً على الجودة التقنية

## 📈 الفوائد المحققة

### ✅ **جودة أفضل**
- صور حقيقية بدلاً من النصوص البسيطة
- ملاءمة عالية للمحتوى
- جودة بصرية احترافية

### ✅ **توفير الوقت**
- بحث تلقائي عن الصور
- اختيار ذكي بدون تدخل يدوي
- معالجة تلقائية كاملة

### ✅ **تحسين SEO**
- صور ذات صلة تحسن ترتيب المقالات
- أسماء ملفات وصفية
- أحجام محسنة للويب

### ✅ **تجربة مستخدم أفضل**
- صور جذابة تزيد التفاعل
- عناوين واضحة ومقروءة
- تصميم احترافي

## 🚀 الخطوات التالية

1. **إعداد API Keys** للحصول على أفضل النتائج
2. **تحميل الخطوط** للحصول على نصوص جميلة
3. **تشغيل الاختبارات** للتأكد من عمل النظام
4. **تخصيص الإعدادات** حسب احتياجاتك

النظام جاهز للاستخدام ويعمل تلقائياً مع كل مقال جديد! 🎉

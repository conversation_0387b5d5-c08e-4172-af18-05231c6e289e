#!/usr/bin/env python3
"""
Blogger Authentication Manager
مدير مصادقة بلوجر

This module handles Blogger OAuth authentication and token management.
"""

import os
import json
import webbrowser
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from utils.logger import logger
import config

# Scopes required for Blogger API
SCOPES = ['https://www.googleapis.com/auth/blogger']

class BloggerAuthManager:
    """Manages Blogger OAuth authentication"""
    
    def __init__(self):
        self.credentials = None
        self.service = None
        
    def load_credentials_from_env(self):
        """Load credentials from environment variable"""
        try:
            if not config.GOOGLE_OAUTH_TOKEN:
                logger.warning("GOOGLE_OAUTH_TOKEN not found in environment")
                return None
                
            token_data = json.loads(config.GOOGLE_OAUTH_TOKEN)
            creds = Credentials.from_authorized_user_info(token_data, SCOPES)
            
            if creds and creds.valid:
                logger.info("✅ Valid credentials loaded from environment")
                return creds
            elif creds and creds.expired and creds.refresh_token:
                logger.info("🔄 Credentials expired, attempting to refresh...")
                try:
                    creds.refresh(Request())
                    logger.info("✅ Credentials refreshed successfully")
                    
                    # Update environment variable with new token
                    self.update_env_token(creds)
                    return creds
                except Exception as e:
                    logger.error(f"❌ Failed to refresh credentials: {e}")
                    return None
            else:
                logger.warning("⚠️ Invalid credentials in environment")
                return None
                
        except json.JSONDecodeError as e:
            logger.error(f"❌ Invalid JSON in GOOGLE_OAUTH_TOKEN: {e}")
            return None
        except Exception as e:
            logger.error(f"❌ Error loading credentials: {e}")
            return None
    
    def authenticate_new_user(self):
        """Start OAuth flow for new authentication"""
        try:
            # Check if client_secret.json exists
            if not os.path.exists('client_secret.json'):
                logger.error("❌ client_secret.json not found!")
                print("\n❌ ملف client_secret.json غير موجود!")
                print("📋 يرجى تحميل ملف client_secret.json من Google Cloud Console:")
                print("   1. اذهب إلى https://console.cloud.google.com/")
                print("   2. اختر مشروعك")
                print("   3. APIs & Services > Credentials")
                print("   4. حمل OAuth 2.0 Client ID كـ JSON")
                print("   5. احفظه باسم 'client_secret.json' في مجلد المشروع")
                return None
            
            logger.info("🔐 Starting OAuth authentication flow...")
            print("\n🔐 بدء عملية المصادقة...")
            print("📱 سيتم فتح متصفح الويب للمصادقة")
            print("🔑 يرجى تسجيل الدخول وإعطاء الصلاحيات المطلوبة")
            
            flow = InstalledAppFlow.from_client_secrets_file(
                'client_secret.json', SCOPES)
            
            # Run local server for OAuth callback
            creds = flow.run_local_server(port=0, open_browser=True)
            
            if creds:
                logger.info("✅ Authentication successful!")
                print("✅ تمت المصادقة بنجاح!")
                
                # Save credentials to environment
                self.update_env_token(creds)
                return creds
            else:
                logger.error("❌ Authentication failed")
                print("❌ فشلت المصادقة")
                return None
                
        except Exception as e:
            logger.error(f"❌ Authentication error: {e}")
            print(f"❌ خطأ في المصادقة: {e}")
            return None
    
    def update_env_token(self, credentials):
        """Update .env file with new token"""
        try:
            # Convert credentials to dict
            token_data = {
                'token': credentials.token,
                'refresh_token': credentials.refresh_token,
                'token_uri': credentials.token_uri,
                'client_id': credentials.client_id,
                'client_secret': credentials.client_secret,
                'scopes': credentials.scopes
            }
            
            token_json = json.dumps(token_data)
            
            # Read current .env file
            env_lines = []
            if os.path.exists('.env'):
                with open('.env', 'r', encoding='utf-8') as f:
                    env_lines = f.readlines()
            
            # Update or add GOOGLE_OAUTH_TOKEN
            token_updated = False
            for i, line in enumerate(env_lines):
                if line.startswith('GOOGLE_OAUTH_TOKEN='):
                    env_lines[i] = f'GOOGLE_OAUTH_TOKEN={token_json}\n'
                    token_updated = True
                    break
            
            if not token_updated:
                env_lines.append(f'GOOGLE_OAUTH_TOKEN={token_json}\n')
            
            # Write back to .env file
            with open('.env', 'w', encoding='utf-8') as f:
                f.writelines(env_lines)
            
            logger.info("✅ Token updated in .env file")
            print("✅ تم تحديث Token في ملف .env")
            
        except Exception as e:
            logger.error(f"❌ Failed to update .env file: {e}")
            print(f"❌ فشل في تحديث ملف .env: {e}")
    
    def delete_credentials(self):
        """Delete credentials from .env file and reset state"""
        try:
            env_lines = []
            token_found = False
            if os.path.exists('.env'):
                with open('.env', 'r', encoding='utf-8') as f:
                    env_lines = f.readlines()

                # Filter out the token line
                new_env_lines = [line for line in env_lines if not line.strip().startswith('GOOGLE_OAUTH_TOKEN=')]

                if len(new_env_lines) < len(env_lines):
                    token_found = True
                    with open('.env', 'w', encoding='utf-8') as f:
                        f.writelines(new_env_lines)

            # Reset internal state
            self.credentials = None
            if hasattr(config, 'GOOGLE_OAUTH_TOKEN'):
                config.GOOGLE_OAUTH_TOKEN = None

            if token_found:
                logger.info("🗑️  Credentials deleted from .env file.")
                print("🗑️  تم حذف بيانات الاعتماد القديمة بنجاح.")
                return True
            else:
                logger.info("🤔 No credentials found to delete.")
                print("🤔 لم يتم العثور على بيانات اعتماد لحذفها.")
                return False

        except Exception as e:
            logger.error(f"❌ Failed to delete credentials: {e}")
            print(f"❌ فشل في حذف بيانات الاعتماد: {e}")
            return False
    
    def get_valid_credentials(self):
        """Get valid credentials, authenticate if necessary"""
        # Try to load from environment first
        creds = self.load_credentials_from_env()
        
        if creds and creds.valid:
            self.credentials = creds
            return creds
        
        # If no valid credentials, start authentication flow
        print("\n🔐 مطلوب مصادقة جديدة لـ Blogger...")
        print("هل تريد بدء عملية المصادقة؟ (y/n): ", end="")
        
        try:
            response = input().lower().strip()
            if response in ['y', 'yes', 'نعم', 'ن']:
                creds = self.authenticate_new_user()
                if creds:
                    self.credentials = creds
                    return creds
            else:
                print("❌ تم إلغاء المصادقة")
                return None
        except KeyboardInterrupt:
            print("\n❌ تم إلغاء المصادقة")
            return None
    
    def get_blogger_service(self):
        """Get authenticated Blogger service"""
        if not self.credentials:
            self.credentials = self.get_valid_credentials()
        
        if not self.credentials:
            return None
        
        try:
            self.service = build('blogger', 'v3', credentials=self.credentials)
            return self.service
        except Exception as e:
            logger.error(f"❌ Failed to build Blogger service: {e}")
            return None
    
    def test_blogger_connection(self):
        """Test connection to Blogger API"""
        try:
            service = self.get_blogger_service()
            if not service:
                return False, "Failed to get Blogger service"
            
            # Test by getting blog info
            blog_id = config.BLOG_ID
            if not blog_id:
                return False, "BLOG_ID not configured"
            
            logger.info(f"🧪 Testing connection to blog ID: {blog_id}")
            
            blog = service.blogs().get(blogId=blog_id).execute()
            blog_name = blog.get('name', 'Unknown')
            blog_url = blog.get('url', 'Unknown')
            
            logger.info(f"✅ Successfully connected to blog: {blog_name}")
            return True, f"Connected to blog: {blog_name} ({blog_url})"
            
        except Exception as e:
            logger.error(f"❌ Blogger connection test failed: {e}")
            return False, str(e)
    
    def test_post_creation(self):
        """Test creating a draft post"""
        try:
            service = self.get_blogger_service()
            if not service:
                return False, "Failed to get Blogger service"
            
            # Create a test draft post
            test_post = {
                "kind": "blogger#post",
                "blog": {"id": config.BLOG_ID},
                "title": "🧪 اختبار الاتصال - Test Connection",
                "content": """
                <h2>اختبار الاتصال بنجاح</h2>
                <p>هذا منشور تجريبي لاختبار الاتصال مع Blogger API.</p>
                <p>تم إنشاؤه تلقائياً بواسطة بوت أخبار كرة القدم.</p>
                <p><em>يمكن حذف هذا المنشور بأمان.</em></p>
                """,
                "labels": ["اختبار", "test", "bot"]
            }
            
            logger.info("🧪 Creating test draft post...")
            
            # Create as draft
            result = service.posts().insert(
                blogId=config.BLOG_ID, 
                body=test_post, 
                isDraft=True
            ).execute()
            
            post_id = result.get('id')
            post_url = result.get('url', 'No URL')
            
            logger.info(f"✅ Test draft post created successfully: {post_id}")
            
            # Try to delete the test post
            try:
                service.posts().delete(blogId=config.BLOG_ID, postId=post_id).execute()
                logger.info("🗑️ Test post deleted successfully")
                return True, f"Test post created and deleted successfully (ID: {post_id})"
            except Exception as delete_error:
                logger.warning(f"⚠️ Test post created but couldn't delete: {delete_error}")
                return True, f"Test post created successfully but couldn't delete (ID: {post_id}). Please delete manually."
            
        except Exception as e:
            logger.error(f"❌ Test post creation failed: {e}")
            return False, str(e)

def main():
    """Main function for managing credentials"""
    print("🔐 Blogger Authentication Manager")
    print("=" * 40)

    auth_manager = BloggerAuthManager()

    while True:
        print("\nالخيارات المتاحة:")
        print("1. 🧪 اختبار الإعدادات الحالية")
        print("2. 🔄 حذف الإعدادات الحالية والبدء من جديد (تغيير الحساب)")
        print("3. 🚪 خروج")
        
        choice = input("اختر رقماً (1-3): ").strip()

        if choice == '1':
            # Test current setup
            print("\n1. 🧪 Testing credential loading...")
            # We need to get valid credentials first, which might trigger auth flow
            creds = auth_manager.get_valid_credentials()
            if not creds:
                print("❌ فشلت عملية الحصول على بيانات الاعتماد. لا يمكن المتابعة.")
                continue
            
            print("✅ Credentials are valid.")

            print("\n2. 🔗 Testing Blogger connection...")
            success, message = auth_manager.test_blogger_connection()
            if success:
                print(f"✅ {message}")
            else:
                print(f"❌ {message}")
                print("⚠️ قد يكون السبب هو استخدام حساب خاطئ أو عدم وجود صلاحيات كافية.")
                continue

            print("\n3. 📝 Testing post creation...")
            success, message = auth_manager.test_post_creation()
            if success:
                print(f"✅ {message}")
            else:
                print(f"❌ {message}")
            
            print("\n🎉 All tests completed!")

        elif choice == '2':
            # Delete and re-authenticate
            print("\n🗑️  حذف بيانات الاعتماد الحالية...")
            auth_manager.delete_credentials()
            print("\n🔐 بدء عملية مصادقة جديدة...")
            new_creds = auth_manager.authenticate_new_user()
            if new_creds:
                # Reset the service instance to use the new credentials
                auth_manager.service = None
                print("\n✅ تمت المصادقة بنجاح! يمكنك الآن اختبار الإعدادات (الخيار 1).")
            else:
                print("\n❌ فشلت عملية المصادقة الجديدة.")

        elif choice == '3':
            print("👋 مع السلامة!")
            break
        else:
            print("❌ خيار غير صالح. يرجى اختيار رقم من 1 إلى 3.")

if __name__ == '__main__':
    main()

#!/usr/bin/env python3
"""
Improved Football News Bot Runner
Runs the bot with better timing control and quality filters
"""

import time
import sys
import signal
from datetime import datetime
from utils.logger import logger
from timing_config import CYCLE_INTERVAL, ARTICLES_PER_CYCLE

def signal_handler(sig, frame):
    """Handle Ctrl+C gracefully"""
    logger.info("\n🛑 Bot stopped by user. Goodbye!")
    sys.exit(0)

def main():
    """Main function to run the improved bot"""
    # Set up signal handler for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    
    logger.info("🚀 Starting Improved Football News Bot")
    logger.info(f"⚙️ Configuration:")
    logger.info(f"   - Articles per cycle: {ARTICLES_PER_CYCLE}")
    logger.info(f"   - Cycle interval: {CYCLE_INTERVAL//60} minutes")
    logger.info(f"   - Focus: Today's news only")
    logger.info("=" * 50)
    
    # Import main cycle function
    try:
        from main import main_cycle
    except ImportError as e:
        logger.error(f"Failed to import main_cycle: {e}")
        return
    
    cycle_count = 0
    
    while True:
        try:
            cycle_count += 1
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            logger.info(f"🔄 Starting cycle #{cycle_count} at {current_time}")
            
            # Run one cycle
            main_cycle()
            
            logger.info(f"✅ Cycle #{cycle_count} completed successfully")
            logger.info(f"⏰ Next cycle will start in {CYCLE_INTERVAL//60} minutes")
            logger.info("=" * 50)
            
            # Wait for next cycle
            time.sleep(CYCLE_INTERVAL)
            
        except KeyboardInterrupt:
            logger.info("\n🛑 Bot stopped by user. Goodbye!")
            break
        except Exception as e:
            logger.error(f"❌ Error in cycle #{cycle_count}: {e}")
            logger.info(f"⏰ Waiting 5 minutes before retrying...")
            time.sleep(300)  # Wait 5 minutes before retrying

if __name__ == "__main__":
    main()

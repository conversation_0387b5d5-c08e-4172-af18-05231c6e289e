
from scraper.mock_requests import get, exceptions
from bs4 import BeautifulSoup
from config import SKY_NEWS_URL
from utils.logger import logger

def scrape_sky_news():
    """Scrapes the latest football news from Sky News Arabia.

    Returns:
        list: A list of dictionaries, where each dictionary represents a news article
              with 'title', 'url', and 'source' keys.
    """
    logger.info("Starting scrape for Sky News Arabia.")
    articles = []
    try:
        response = get(SKY_NEWS_URL, timeout=15)
        response.raise_for_status() # Raise an exception for bad status codes

        soup = BeautifulSoup(response.content, 'html.parser')

        # Try multiple selectors to find news articles
        selectors_to_try = [
            'article a',  # All article links
            '.story a',  # Story links
            '.news a',  # News links
            'h1 a', 'h2 a', 'h3 a', 'h4 a',  # Headline links
            '.title a',  # Title links
            '.headline a',  # Headlines
            'a[href*="/sport"]',  # Sport section links (broader)
            'a[href*="/football"]',  # Football specific links
            'a[href*="/news"]',  # News links
            'a[href*="/story"]',  # Story links
            '.card a',  # Card links
            '.item a'  # Item links
        ]

        news_links = []
        for selector in selectors_to_try:
            try:
                found_links = soup.select(selector)
                if found_links:
                    logger.info(f"Found {len(found_links)} links with selector: {selector}")
                    news_links.extend(found_links[:10])  # Limit to 10 per selector
                    break  # Use the first working selector
            except Exception as e:
                logger.warning(f"Error with selector {selector}: {e}")
                continue

        # If no specific selectors work, try general approach
        if not news_links:
            logger.warning("No news found with specific selectors, trying general approach")
            all_links = soup.find_all('a', href=True)

            # Filter for potential news links
            potential_news = []
            for link in all_links:
                href = link.get('href', '').lower()
                text = link.get_text(strip=True).lower()
                title = link.get('title', '').lower()

                # Check if this looks like a news article
                news_indicators = [
                    'sport' in href, 'football' in href, 'news' in href,
                    'كرة' in text, 'مباراة' in text, 'فوز' in text, 'هدف' in text,
                    'لاعب' in text, 'فريق' in text, 'دوري' in text, 'كأس' in text,
                    'sport' in title, 'football' in title
                ]

                if any(news_indicators) and len(text) > 10:
                    potential_news.append(link)

            news_links = potential_news[:15]  # Get more to filter later

        for link in news_links:
            try:
                title = link.get_text(strip=True)
                url = link.get('href')

                # Skip navigation and non-news links
                if not title or len(title) < 10:
                    continue

                # Skip common navigation items and non-news content
                skip_keywords = [
                    'تسجيل الدخول', 'إعدادات', 'راديو', 'مباشر', 'صفحة أخباري', 'البث المباشر',
                    'قائمة', 'بحث', 'الرئيسية', 'اتصل', 'عنا', 'سياسة', 'شروط', 'خصوصية',
                    'login', 'register', 'search', 'menu', 'home', 'contact', 'about', 'privacy'
                ]
                if any(keyword in title.lower() for keyword in skip_keywords):
                    continue

                # Clean up the title - remove extra text and timestamps
                title = title.split('l')[0]  # Remove timestamp part
                title = title.replace('عاجل', '').strip()  # Remove "urgent" tag
                title = title.replace('كرة قدم أوروبية', '').strip()  # Remove category
                title = title.replace('دوري إسباني', '').strip()
                title = title.replace('دوري إيطالي', '').strip()
                title = title.replace('دوري مصري', '').strip()
                title = title.replace('دوري سعودي', '').strip()
                title = title.replace('رياضة', '').strip()

                # Skip if title becomes too short after cleaning
                if len(title) < 10:
                    continue

                # Make sure the URL is absolute
                if url and not url.startswith('http'):
                    url = f"https://www.skynewsarabia.com{url}"

                # Check for football/sports content
                football_keywords = [
                    'كرة', 'مباراة', 'فوز', 'هدف', 'لاعب', 'فريق', 'دوري', 'كأس',
                    'بطولة', 'مدرب', 'نادي', 'منتخب', 'ملعب', 'حكم', 'إصابة',
                    'انتقال', 'صفقة', 'عقد', 'تجديد'
                ]

                has_football_content = any(keyword in title.lower() for keyword in football_keywords)
                has_sport_url = any(indicator in url.lower() for indicator in ['sport', 'football', 'news'])

                # Validate that this looks like a news article
                if url and (has_sport_url or has_football_content):
                    articles.append({
                        'title': title,
                        'url': url,
                        'source': 'Sky News'
                    })
                    logger.info(f"Found article: {title}")
            except Exception as e:
                logger.warning(f"Error processing link: {e}")
                continue

    except exceptions.RequestException as e:
        logger.error(f"Failed to scrape Sky News Arabia: {e}")
    except Exception as e:
        logger.error(f"An unexpected error occurred during Sky News scraping: {e}")

    logger.info(f"Found {len(articles)} articles from Sky News Arabia.")
    return articles

if __name__ == '__main__':
    # For testing purposes
    scraped_articles = scrape_sky_news()
    if scraped_articles:
        for article in scraped_articles:
            print(article)


from scraper.mock_selenium import webdriver, Options, By, Service, ChromeDriverManager
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException
from config import KOOORA_URL, PROXY_SERVER, PROXY_PORT
from utils.logger import logger
import time
import random
import re

def clean_kooora_title(title):
    """Cleans a Kooora article title by removing timestamps, dates, and common prefixes."""
    if not title:
        return ""

    # Remove timestamps (e.g., 10:39) and surrounding characters
    title = re.sub(r'\s*\d{1,2}:\d{2}\s*', ' ', title).strip()

    # Remove dates (e.g., 8 يوليو 2025)
    # This regex is designed to be general for Arabic month names
    title = re.sub(r'\s*\d{1,2}\s+[\u0621-\u064A]+\s+\d{4}\s*', ' ', title, flags=re.IGNORECASE).strip()

    # Remove common prefixes, ignoring case
    prefixes = ['خاص كووورة', 'كووورة']
    for prefix in prefixes:
        if title.lower().startswith(prefix.lower()):
            title = title[len(prefix):].strip()
            # Break after finding the first prefix to avoid multiple removals
            break
    
    # Remove "مفترق طرق.." if it appears at the beginning
    if title.lower().startswith("مفترق طرق.."):
        title = title[len("مفترق طرق.."):].strip()

    # Replace multiple dots with a single one
    title = re.sub(r'\.{2,}', '.', title)

    # Final cleanup of extra whitespace
    title = ' '.join(title.split())

    return title

def scrape_kooora():
    """Scrapes the latest football news from Kooora.

    Returns:
        list: A list of dictionaries, where each dictionary represents a news article
              with 'title', 'url', and 'source' keys.
    """
    logger.info("Starting scrape for Kooora.")
    articles = []

    # Setup Chrome options for headless mode and resource optimization
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--window-size=1920,1080")
    chrome_options.add_argument("--disable-web-security")
    chrome_options.add_argument("--disable-features=VizDisplayCompositor")

    # Use a more recent user agent
    chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")

    # Disable automation detection
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option('useAutomationExtension', False)

    # Add proxy if configured
    if PROXY_SERVER and PROXY_PORT:
        proxy_address = f"{PROXY_SERVER}:{PROXY_PORT}"
        chrome_options.add_argument(f"--proxy-server={proxy_address}")
        logger.info(f"Using proxy: {proxy_address}")

    # SSL and security settings
    chrome_options.add_argument("--ignore-certificate-errors")
    chrome_options.add_argument("--ignore-ssl-errors")
    chrome_options.add_argument("--allow-running-insecure-content")
    chrome_options.add_argument("--disable-extensions")

    # Memory and resource management
    chrome_options.add_argument("--memory-pressure-off")
    chrome_options.add_argument("--max_old_space_size=4096")

    # Disable unnecessary features (only disable notifications and popups)
    chrome_options.add_experimental_option("prefs", {
        "profile.default_content_setting_values.notifications": 2,
        "profile.default_content_settings.popups": 2,
        "profile.managed_default_content_settings.media_stream": 2,
    })

    # Maximum number of retries
    max_retries = 3
    retry_count = 0

    driver = None
    while retry_count < max_retries:
        try:
            # Use webdriver-manager to handle the driver automatically
            service = Service(ChromeDriverManager().install())
            driver = webdriver.Chrome(service=service, options=chrome_options)

            # Set timeouts - increased for better reliability
            driver.set_page_load_timeout(120)  # Increased to 2 minutes
            driver.implicitly_wait(10)

            # Navigate to the URL
            logger.info(f"Attempting to access {KOOORA_URL} (Attempt {retry_count + 1}/{max_retries})")

            # Try to load the page with error handling
            try:
                driver.get(KOOORA_URL)
                logger.info("Page loaded successfully")
            except Exception as e:
                logger.warning(f"Page load issue: {e}")
                # Try to continue anyway

            # Add a delay to let the page settle
            time.sleep(random.uniform(3, 7))

            # Try multiple selectors to find news items
            news_elements = []
            selectors_to_try = [
                'a[href*="/news/"]',  # Links containing /news/
                'a[href*="/sport/"]',  # Sport links
                'a[href*="/football/"]',  # Football links
                '.news-item a',
                '.news-title a',
                'article a',
                '.article-title a',
                'h3 a',
                'h2 a',
                '.title a',
                'a[title*="كرة"]',  # Links with football in title
                'a[title*="مباراة"]',  # Match links
                'a'  # All links as fallback
            ]

            for selector in selectors_to_try:
                try:
                    logger.info(f"Trying selector: {selector}")
                    # Reduce timeout to 10 seconds per selector
                    WebDriverWait(driver, 10).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    found_elements = driver.find_elements(By.CSS_SELECTOR, selector)

                    # Filter elements that look like news
                    for elem in found_elements:
                        try:
                            href = elem.get_attribute('href')
                            text = elem.text.strip()

                            # Check if this looks like a news link
                            if (href and text and len(text) > 10 and
                                ('kooora.com' in href or href.startswith('/')) and
                                ('news' in href.lower() or 'sport' in href.lower() or
                                 'كرة' in text.lower() or 'مباراة' in text.lower() or
                                 'فوز' in text.lower() or 'هدف' in text.lower())):
                                news_elements.append(elem)

                        except Exception:
                            continue

                    if news_elements:
                        logger.info(f"Found {len(news_elements)} news elements with selector: {selector}")
                        news_elements = news_elements[:10]  # Limit to 10
                        break

                except TimeoutException:
                    logger.warning(f"Selector {selector} not found, trying next...")
                    continue
                except Exception as e:
                    logger.warning(f"Error with selector {selector}: {e}")
                    continue

            # If no elements found with any selector, try to get all links and filter
            if not news_elements:
                logger.warning("No news elements found with any selector. Trying to get all links...")
                try:
                    all_links = driver.find_elements(By.TAG_NAME, 'a')[:50]  # Limit to first 50 links
                    for link in all_links:
                        try:
                            href = link.get_attribute('href')
                            text = link.text.strip()

                            if (href and text and len(text) > 10 and
                                ('kooora.com' in href or href.startswith('/')) and
                                ('news' in href.lower() or 'sport' in href.lower() or
                                 'football' in href.lower() or 'كرة' in text.lower() or
                                 'مباراة' in text.lower() or 'فوز' in text.lower())):
                                news_elements.append(link)
                                if len(news_elements) >= 10:
                                    break
                        except Exception:
                            continue
                    logger.info(f"Found {len(news_elements)} potential news links")
                except Exception as e:
                    logger.error(f"Error getting all links: {e}")

            for elem in news_elements:
                try:
                    # Get the URL first
                    url = elem.get_attribute('href')
                    if not url or not url.startswith('http'):
                        continue

                    # Try to get title from different sources
                    title = ""

                    # Try to find title in child elements first
                    title_selectors = [
                        'h1', 'h2', 'h3', 'h4', 'h5', 
                        '.title', '.news-title', '.headline', 
                        '[class*="title"]', 'span'  # More generic selectors
                    ]
                    for selector in title_selectors:
                        try:
                            # Find all potential title elements and pick the one with the most text
                            title_elems = elem.find_elements(By.CSS_SELECTOR, selector)
                            if title_elems:
                                best_title = ""
                                for title_elem in title_elems:
                                    elem_text = title_elem.text.strip()
                                    if elem_text and len(elem_text) > len(best_title):
                                        best_title = elem_text
                                if best_title:
                                    title = best_title
                                    break 
                        except:
                            continue
                    
                    # If no title found in child elements, use the element's text as a fallback
                    if not title:
                        title = elem.text.strip()

                    # If still no title, try to get it from title attribute
                    if not title:
                        title = elem.get_attribute('title') or ""

                    # Clean up the title using the dedicated function
                    title = clean_kooora_title(title)

                    # Validate title and URL
                    if title and len(title) > 10 and url and 'kooora.com' in url:
                        articles.append({
                            'title': title,
                            'url': url,
                            'source': 'Kooora'
                        })
                        logger.info(f"Found article: {title[:50]}...")

                except Exception as e:
                    logger.warning(f"Could not process a news item from Kooora: {e}")
                    continue

            # If we got here without exceptions, break the retry loop
            break
            
        except TimeoutException as e:
            retry_count += 1
            logger.warning(f"Timeout while accessing Kooora (Attempt {retry_count}/{max_retries}): {str(e)[:200]}...")
            if driver:
                try:
                    driver.quit()
                except:
                    pass
                driver = None
            if retry_count < max_retries:
                wait_time = 10 * retry_count  # Exponential backoff
                logger.info(f"Waiting {wait_time} seconds before retry...")
                time.sleep(wait_time)

        except WebDriverException as e:
            retry_count += 1
            logger.warning(f"WebDriver error while accessing Kooora (Attempt {retry_count}/{max_retries}): {str(e)[:200]}...")
            if driver:
                try:
                    driver.quit()
                except:
                    pass
                driver = None
            if retry_count < max_retries:
                wait_time = 10 * retry_count
                logger.info(f"Waiting {wait_time} seconds before retry...")
                time.sleep(wait_time)

        except Exception as e:
            logger.error(f"Unexpected error while scraping Kooora: {e}")
            retry_count += 1
            if driver:
                try:
                    driver.quit()
                except:
                    pass
                driver = None
            if retry_count < max_retries:
                logger.info("Retrying after unexpected error...")
                time.sleep(5)

        finally:
            # Clean up driver if still exists
            if driver:
                try:
                    driver.quit()
                except:
                    pass
                driver = None

    # Log results
    if articles:
        logger.info(f"Successfully found {len(articles)} articles from Kooora after {retry_count + 1} attempts.")
    else:
        logger.warning(f"Found 0 articles from Kooora after {retry_count + 1} attempts.")

    return articles

if __name__ == '__main__':
    # For testing purposes
    scraped_articles = scrape_kooora()
    if scraped_articles:
        for article in scraped_articles:
            print(article)

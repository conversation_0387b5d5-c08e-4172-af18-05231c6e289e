#!/usr/bin/env python3
"""
Test script for Blogger Authentication Manager
"""

from blogger_auth_manager import BloggerAuthManager
from utils.logger import logger

def test_auth_manager():
    """Test the authentication manager"""
    print("🧪 Testing Blogger Authentication Manager")
    print("=" * 50)
    
    try:
        auth_manager = BloggerAuthManager()
        
        print("\n1. 📋 Testing credential loading...")
        creds = auth_manager.load_credentials_from_env()
        if creds:
            if creds.valid:
                print("✅ Valid credentials found")
            else:
                print("⚠️ Credentials found but invalid/expired")
        else:
            print("❌ No credentials found")
        
        print("\n2. 🔗 Testing Blogger connection...")
        success, message = auth_manager.test_blogger_connection()
        print(f"Result: {'✅' if success else '❌'} {message}")
        
        if success:
            print("\n3. 📝 Testing post creation...")
            post_success, post_message = auth_manager.test_post_creation()
            print(f"Result: {'✅' if post_success else '❌'} {post_message}")
        else:
            print("\n3. ⏭️ Skipping post creation test (connection failed)")
        
        print("\n" + "=" * 50)
        if success:
            print("🎉 Blogger authentication is working!")
        else:
            print("⚠️ Blogger authentication needs setup")
            print("📖 See BLOGGER_SETUP_GUIDE.md for instructions")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        logger.error(f"Error during auth testing: {e}")

if __name__ == '__main__':
    test_auth_manager()

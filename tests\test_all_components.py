#!/usr/bin/env python3
"""
Comprehensive test suite for all components
"""

import unittest
import asyncio
import os
import sys
import tempfile
import shutil

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from scraper import sky_news_scraper
from scraper import kooora_scraper_simple as kooora_scraper
from generator import content_generator, image_generator
from publisher import blogger_publisher, telegram_publisher
from database import database
from utils.logger import logger

class TestScrapers(unittest.TestCase):
    """Test news scrapers"""
    
    def test_sky_news_scraper(self):
        """Test Sky News scraper"""
        logger.info("Testing Sky News scraper...")
        articles = sky_news_scraper.scrape_sky_news()
        
        self.assertIsInstance(articles, list)
        if articles:  # If articles found
            self.assertGreater(len(articles), 0)
            for article in articles[:3]:  # Test first 3 articles
                self.assertIn('title', article)
                self.assertIn('url', article)
                self.assertIn('source', article)
                self.assertEqual(article['source'], 'Sky News')
                self.assertIsInstance(article['title'], str)
                self.assertIsInstance(article['url'], str)
                self.assertGreater(len(article['title']), 5)
                self.assertTrue(article['url'].startswith('http'))
    
    def test_kooora_scraper(self):
        """Test Kooora scraper"""
        logger.info("Testing Kooora scraper...")
        articles = kooora_scraper.scrape_kooora()
        
        self.assertIsInstance(articles, list)
        if articles:  # If articles found
            self.assertGreater(len(articles), 0)
            for article in articles[:3]:  # Test first 3 articles
                self.assertIn('title', article)
                self.assertIn('url', article)
                self.assertIn('source', article)
                self.assertEqual(article['source'], 'Kooora')
                self.assertIsInstance(article['title'], str)
                self.assertIsInstance(article['url'], str)
                self.assertGreater(len(article['title']), 5)
                self.assertTrue(article['url'].startswith('http'))

class TestGenerators(unittest.TestCase):
    """Test content and image generators"""
    
    def test_content_generator(self):
        """Test content generator"""
        logger.info("Testing content generator...")
        title = "اختبار توليد المحتوى - مباراة كرة قدم"
        source = "Test Source"
        
        article, keywords = content_generator.generate_article(title, source)
        
        self.assertIsNotNone(article)
        self.assertIsInstance(article, str)
        self.assertGreater(len(article), 50)  # Should be substantial content
        self.assertIsInstance(keywords, list)
        self.assertGreater(len(keywords), 0)
    
    def test_image_generator(self):
        """Test image generator"""
        logger.info("Testing image generator...")
        title = "اختبار توليد الصور"
        
        # Create temporary directory for test
        with tempfile.TemporaryDirectory() as temp_dir:
            image_path = image_generator.generate_post_image(title, temp_dir)
            
            if image_path:  # If image generation succeeded
                self.assertIsNotNone(image_path)
                self.assertTrue(os.path.exists(image_path))
                self.assertTrue(image_path.endswith('.png'))

class TestDatabase(unittest.TestCase):
    """Test database operations"""
    
    def setUp(self):
        """Set up test database"""
        self.test_db = 'test_news.db'
        # Temporarily change database name for testing
        import config
        self.original_db = config.DATABASE_NAME
        config.DATABASE_NAME = self.test_db
        database.DATABASE_NAME = self.test_db
        
        database.init_db()
    
    def tearDown(self):
        """Clean up test database"""
        if os.path.exists(self.test_db):
            os.remove(self.test_db)
        # Restore original database name
        import config
        config.DATABASE_NAME = self.original_db
        database.DATABASE_NAME = self.original_db
    
    def test_database_operations(self):
        """Test database CRUD operations"""
        logger.info("Testing database operations...")
        
        # Test data
        title = "اختبار قاعدة البيانات"
        url = "https://test.com/article123"
        source = "Test Source"
        
        # Test article doesn't exist initially
        self.assertFalse(database.article_exists(url))
        
        # Test adding article
        result = database.add_news_article(title, url, source)
        self.assertTrue(result)
        
        # Test article now exists
        self.assertTrue(database.article_exists(url))
        
        # Test duplicate prevention
        duplicate_result = database.add_news_article(title, url, source)
        self.assertFalse(duplicate_result)

class TestPublishers(unittest.TestCase):
    """Test publishers"""
    
    def test_blogger_publisher(self):
        """Test Blogger publisher (with fallback)"""
        logger.info("Testing Blogger publisher...")
        
        title = "اختبار ناشر بلوجر"
        content = "محتوى تجريبي للاختبار"
        keywords = ["اختبار", "بلوجر"]
        image_path = None
        
        # This should return a URL (either real or mock)
        result = blogger_publisher.publish_to_blogger(title, content, keywords, image_path)
        self.assertIsNotNone(result)
        self.assertIsInstance(result, str)
        self.assertTrue(result.startswith('http'))
    
    def test_telegram_publisher(self):
        """Test Telegram publisher"""
        logger.info("Testing Telegram publisher...")
        
        title = "اختبار ناشر تيليجرام"
        url = "https://test.com/article"
        
        # Run async test
        async def async_test():
            try:
                await telegram_publisher.send_telegram_notification(title, url)
                return True
            except Exception as e:
                logger.warning(f"Telegram test failed (expected if no valid token): {e}")
                return False
        
        # This might fail if no valid Telegram token, which is okay for testing
        result = asyncio.run(async_test())
        # We don't assert True here because it might fail due to token issues
        self.assertIsInstance(result, bool)

class TestIntegration(unittest.TestCase):
    """Integration tests"""
    
    def test_full_workflow_simulation(self):
        """Test a simulated full workflow"""
        logger.info("Testing full workflow simulation...")
        
        # 1. Simulate scraping (use mock data)
        mock_articles = [
            {
                'title': 'اختبار التكامل - مباراة كرة قدم',
                'url': 'https://test.com/integration-test',
                'source': 'Test Source'
            }
        ]
        
        # 2. Test database check
        database.init_db()
        article = mock_articles[0]
        
        if not database.article_exists(article['url']):
            # 3. Test content generation
            content, keywords = content_generator.generate_article(
                article['title'], article['source']
            )
            self.assertIsNotNone(content)
            self.assertIsInstance(keywords, list)
            
            # 4. Test image generation
            with tempfile.TemporaryDirectory() as temp_dir:
                image_path = image_generator.generate_post_image(
                    article['title'], temp_dir
                )
                
                # 5. Test publishing (mock)
                post_url = blogger_publisher.publish_to_blogger(
                    article['title'], content, keywords, image_path
                )
                self.assertIsNotNone(post_url)
                
                # 6. Test database update
                db_result = database.add_news_article(
                    article['title'], article['url'], article['source']
                )
                self.assertTrue(db_result)
                
                # 7. Verify article is now in database
                self.assertTrue(database.article_exists(article['url']))

def run_all_tests():
    """Run all tests"""
    logger.info("Starting comprehensive test suite...")
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestScrapers,
        TestGenerators,
        TestDatabase,
        TestPublishers,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    if result.wasSuccessful():
        logger.info("🎉 All tests passed!")
        print("🎉 ALL TESTS PASSED!")
        return True
    else:
        logger.error(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        print(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        return False

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)

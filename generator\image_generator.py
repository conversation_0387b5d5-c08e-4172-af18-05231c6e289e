
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from PIL import Image, ImageDraw, ImageFont
from utils.logger import logger
from utils.smart_image_manager import smart_image_manager

def generate_post_image(title, output_path="imgs", article_content="", use_smart_generation=True):
    """Generates an intelligent image for the article using AI-powered search and analysis.

    Args:
        title (str): The title to be written on the image.
        output_path (str): The directory to save the image in.
        article_content (str): The full article content for better entity extraction.
        use_smart_generation (bool): Whether to use smart AI-powered image generation.

    Returns:
        str: The path to the generated image, or None if failed.
    """
    logger.info(f"Generating image for title: {title}")

    # Create the output directory if it doesn't exist
    if not os.path.exists(output_path):
        os.makedirs(output_path)

    # Try smart image generation first
    if use_smart_generation:
        try:
            logger.info("Attempting smart image generation...")

            # Generate output path
            import time
            timestamp = int(time.time())
            smart_output_path = os.path.join(output_path, f"smart_image_{timestamp}.jpg")

            # Use smart image manager
            smart_image_path = smart_image_manager.generate_smart_image(
                title, article_content, smart_output_path
            )

            if smart_image_path and os.path.exists(smart_image_path):
                logger.info(f"Smart image generation successful: {smart_image_path}")
                return smart_image_path
            else:
                logger.warning("Smart image generation failed, falling back to simple generation")

        except Exception as e:
            logger.error(f"Smart image generation error: {e}")
            logger.info("Falling back to simple image generation")

    # Fallback to simple image generation
    return generate_simple_image(title, output_path)

def generate_simple_image(title, output_path="imgs"):
    """Generates a simple image with the article title (fallback method).

    Args:
        title (str): The title to be written on the image.
        output_path (str): The directory to save the image in.

    Returns:
        str: The path to the generated image, or None if failed.
    """
    logger.info(f"Generating simple image for title: {title}")
    try:
        # Create the output directory if it doesn't exist
        if not os.path.exists(output_path):
            os.makedirs(output_path)

        # Image settings
        img_width = 1280
        img_height = 720  # 16:9 aspect ratio (updated to match smart images)
        background_color = (14, 25, 45)  # Dark blue
        text_color = (255, 255, 255)  # White

        # Create a new image
        image = Image.new('RGB', (img_width, img_height), color=background_color)
        draw = ImageDraw.Draw(image)

        # Font settings (this may need adjustment on the deployment server)
        try:
            # Try to use a common font
            font = ImageFont.truetype("DejaVuSans.ttf", 60)
        except IOError:
            logger.warning("DejaVuSans.ttf not found. Using default font.")
            font = ImageFont.load_default()

        # Text wrapping
        # Simple wrapping logic for demonstration
        words = title.split()
        lines = []
        current_line = ''
        for word in words:
            if len(current_line + word) < 30:
                current_line += f" {word}"
            else:
                lines.append(current_line.strip())
                current_line = word
        lines.append(current_line.strip())

        # Calculate text position (center)
        total_text_height = len(lines) * 60
        y_text = (img_height - total_text_height) / 2

        for line in lines:
            text_width = draw.textlength(line, font=font)
            draw.text(((img_width - text_width) / 2, y_text), line, font=font, fill=text_color)
            y_text += 60

        # Save the image
        image_filename = f"{title.replace(' ', '_')[:50]}.png"
        image_path = os.path.join(output_path, image_filename)
        image.save(image_path)
        logger.info(f"Simple image saved to: {image_path}")
        return image_path

    except Exception as e:
        logger.error(f"Failed to generate simple image: {e}")
        return None

if __name__ == '__main__':
    # For testing purposes
    if not os.path.exists('imgs'):
        os.makedirs('imgs')
    test_title = "الزمالك يتأهل لنهائي كأس مصر على حساب بيراميدز"
    img_file = generate_post_image(test_title)
    if img_file:
        print(f"Image generated successfully: {img_file}")


from PIL import Image, ImageDraw, ImageFont
from utils.logger import logger
import os

def generate_post_image(title, output_path="imgs"):
    """Generates a simple image with the article title.

    Args:
        title (str): The title to be written on the image.
        output_path (str): The directory to save the image in.

    Returns:
        str: The path to the generated image, or None if failed.
    """
    logger.info(f"Generating image for title: {title}")
    try:
        # Create the output directory if it doesn't exist
        if not os.path.exists(output_path):
            os.makedirs(output_path)

        # Image settings
        img_width = 1200
        img_height = 675 # 16:9 aspect ratio
        background_color = (14, 25, 45) # Dark blue
        text_color = (255, 255, 255) # White

        # Create a new image
        image = Image.new('RGB', (img_width, img_height), color=background_color)
        draw = ImageDraw.Draw(image)

        # Font settings (this may need adjustment on the deployment server)
        try:
            # Try to use a common font
            font = ImageFont.truetype("DejaVuSans.ttf", 60)
        except IOError:
            logger.warning("DejaVuSans.ttf not found. Using default font.")
            font = ImageFont.load_default()

        # Text wrapping
        # Simple wrapping logic for demonstration
        words = title.split()
        lines = []
        current_line = ''
        for word in words:
            if len(current_line + word) < 30:
                current_line += f" {word}"
            else:
                lines.append(current_line.strip())
                current_line = word
        lines.append(current_line.strip())

        # Calculate text position (center)
        total_text_height = len(lines) * 60
        y_text = (img_height - total_text_height) / 2

        for line in lines:
            text_width = draw.textlength(line, font=font)
            draw.text(((img_width - text_width) / 2, y_text), line, font=font, fill=text_color)
            y_text += 60

        # Save the image
        image_filename = f"{title.replace(' ', '_')[:50]}.png"
        image_path = os.path.join(output_path, image_filename)
        image.save(image_path)
        logger.info(f"Image saved to: {image_path}")
        return image_path

    except Exception as e:
        logger.error(f"Failed to generate image: {e}")
        return None

if __name__ == '__main__':
    # For testing purposes
    if not os.path.exists('imgs'):
        os.makedirs('imgs')
    test_title = "الزمالك يتأهل لنهائي كأس مصر على حساب بيراميدز"
    img_file = generate_post_image(test_title)
    if img_file:
        print(f"Image generated successfully: {img_file}")

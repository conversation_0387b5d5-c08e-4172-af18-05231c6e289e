
import subprocess
import time
import psutil
import os

def run_scraper_and_monitor():
    """Runs the kooora_scraper and monitors its memory usage."""
    # Note: This test runs the actual scraper, not the mock.
    # It requires a functional environment where selenium can run.
    # We will modify the scraper to use the real selenium for this test.

    # Temporarily switch kooora_scraper to use real selenium
    with open('scraper/kooora_scraper.py', 'r') as f:
        original_content = f.read()
    
    modified_content = original_content.replace(
        "from scraper import mock_selenium as selenium",
        "from selenium import webdriver"
    )
    with open('scraper/kooora_scraper.py', 'w') as f:
        f.write(modified_content)

    process = None
    try:
        # Start the scraper as a separate process
        process = subprocess.Popen(['python', '-c', 'from scraper import kooora_scraper; kooora_scraper.scrape_kooora()'], 
                                     stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        print(f"Started Kooora scraper process with PID: {process.pid}")

        max_memory_usage = 0
        p = psutil.Process(process.pid)

        # Monitor memory usage while the process is running
        while process.poll() is None:
            try:
                memory_info = p.memory_info()
                memory_mb = memory_info.rss / (1024 * 1024) # Resident Set Size in MB
                if memory_mb > max_memory_usage:
                    max_memory_usage = memory_mb
                print(f"Current memory usage: {memory_mb:.2f} MB", end='\r')
                time.sleep(0.5)
            except psutil.NoSuchProcess:
                break
        
        print(f"\nScraper process finished.")
        print(f"Maximum memory usage: {max_memory_usage:.2f} MB")

        if max_memory_usage > 100:
            print("WARNING: Memory usage exceeded the 100 MB limit!")
        else:
            print("SUCCESS: Memory usage is within the 100 MB limit.")

    except Exception as e:
        print(f"An error occurred during the test: {e}")
    finally:
        # Restore the original scraper file
        with open('scraper/kooora_scraper.py', 'w') as f:
            f.write(original_content)
        if process:
            process.kill()

if __name__ == "__main__":
    print("--- Starting Memory Usage Test for Kooora Scraper ---")
    # Since we can't run this in the current environment, 
    # this script stands as a template for how the test SHOULD be run.
    print("NOTE: This test cannot be executed in the current environment due to package installation issues.")
    print("The script is provided as a complete solution for a functional environment.")
    # run_scraper_and_monitor()

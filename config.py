
import os
from dotenv import load_dotenv
load_dotenv()

# Telegram
TELEGRAM_BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
TELEGRAM_CHANNEL_ID = os.getenv('TELEGRAM_CHANNEL_ID')

# Blogger
BLOG_ID = os.getenv('BLOG_ID')
GOOGLE_OAUTH_TOKEN = os.getenv('GOOGLE_OAUTH_TOKEN') # This will be the JSON string

# Gemini
GEMINI_API_KEYS = [val for key, val in os.environ.items() if key.startswith("GEMINI_API_KEY")] or ["mock_gemini_key"]

# Database
DATABASE_NAME = 'database/news.db'

# URLs
SKY_NEWS_URL = 'https://www.skynewsarabia.com/sport/football'
KOOORA_URL = 'https://www.kooora.com/%D8%A3%D8%AE%D8%A8%D8%A7%D8%B1'

# Proxy settings (optional)
PROXY_SERVER = os.getenv('PROXY_SERVER')
PROXY_PORT = os.getenv('PROXY_PORT')

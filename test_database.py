#!/usr/bin/env python3
"""
Test script for database operations
"""

from database.database import init_db, add_news_article, article_exists
from utils.logger import logger
import os

def test_database():
    """Test database operations."""
    logger.info("Testing database operations...")
    
    try:
        # Initialize database
        init_db()
        logger.info("✅ Database initialization successful")
        
        # Test data
        test_articles = [
            {
                'title': 'اختبار مقال 1 - الأهلي يفوز على الزمالك',
                'url': 'https://test.com/article1',
                'source': 'Test Source'
            },
            {
                'title': 'اختبار مقال 2 - برشلونة يتعاقد مع لاعب جديد',
                'url': 'https://test.com/article2',
                'source': 'Test Source'
            }
        ]
        
        # Test adding articles
        for article in test_articles:
            # Check if article doesn't exist initially
            if not article_exists(article['url']):
                logger.info(f"Article '{article['title']}' doesn't exist (as expected)")
                
                # Add the article
                success = add_news_article(article['title'], article['url'], article['source'])
                if success:
                    logger.info(f"✅ Successfully added article: {article['title']}")
                else:
                    logger.error(f"❌ Failed to add article: {article['title']}")
                    return False
                
                # Check if article now exists
                if article_exists(article['url']):
                    logger.info(f"✅ Article existence check passed for: {article['title']}")
                else:
                    logger.error(f"❌ Article existence check failed for: {article['title']}")
                    return False
            else:
                logger.info(f"Article '{article['title']}' already exists")
        
        # Test duplicate prevention
        logger.info("Testing duplicate prevention...")
        duplicate_result = add_news_article(test_articles[0]['title'], test_articles[0]['url'], test_articles[0]['source'])
        if not duplicate_result:
            logger.info("✅ Duplicate prevention working correctly")
        else:
            logger.error("❌ Duplicate prevention failed")
            return False
        
        logger.info("✅ All database tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Database test failed with exception: {e}")
        return False

def main():
    """Main function to run the database test."""
    success = test_database()
    if success:
        print("✅ Database test PASSED")
    else:
        print("❌ Database test FAILED")

if __name__ == '__main__':
    main()

# تصميم هيكل ومكونات بوت أخبار كرة القدم

## 1. نظرة عامة على الهيكل

سيعتمد البوت على هيكل معياري (modular) يتكون من عدة مكونات رئيسية، كل مكون مسؤول عن مهمة محددة. هذا التصميم يسهل الصيانة والتطوير والتوسع المستقبلي. سيتم تنظيم الكود في مجلدات منفصلة لكل مكون.

```
football-news-bot/
├── main.py                 # نقطة الدخول الرئيسية وتشغيل البوت
├── config.py               # إدارة الإعدادات والمفاتيح
├── requirements.txt        # المكتبات المطلوبة
├── database/
│   └── database.py         # إدارة قاعدة بيانات SQLite
│   └── news.db             # ملف قاعدة البيانات
├── scraper/
│   └── sky_news_scraper.py # استخلاص الأخبار من Sky News
│   └── kooora_scraper.py   # استخلاص الأخبار من Kooora (باستخدام Selenium/Puppeteer)
├── generator/
│   └── content_generator.py # توليد المحتوى باستخدام Gemini
│   └── image_generator.py  # توليد الصور
├── publisher/
│   └── blogger_publisher.py # النشر على بلوجر
│   └── telegram_publisher.py # إرسال الإشعارات عبر تليجرام
└── utils/
    └── seo_utils.py        # أدوات مساعدة لتحسين محركات البحث
    └── logging_utils.py    # إعدادات التسجيل والمراقبة
```

## 2. وصف المكونات

*   **`main.py`**:  البرنامج النصي الرئيسي الذي ينسق عمل جميع المكونات الأخرى. سيقوم بتشغيل حلقة لا نهائية (أو مهمة مجدولة) لجلب الأخبار، توليد المحتوى، ونشره.

*   **`config.py`**: سيحتوي هذا الملف على جميع معلومات التكوين الحساسة، مثل مفاتيح API، معرفات المدونة والقناة، وتكوينات أخرى. سيتم تحميل هذه القيم من متغيرات البيئة لضمان الأمان.

*   **`database/`**: هذا المكون مسؤول عن التفاعل مع قاعدة بيانات SQLite. سيحتوي على وظائف لإنشاء الجداول، إضافة أخبار جديدة، والتحقق من وجود خبر معين لمنع التكرار.

*   **`scraper/`**: سيحتوي هذا المجلد على وحدات لاستخلاص الأخبار من المصادر المحددة. `sky_news_scraper.py` سيستخدم مكتبات مثل `requests` و `BeautifulSoup` للاستخلاص المباشر، بينما `kooora_scraper.py` قد يتطلب استخدام أدوات أكثر تعقيدًا مثل `selenium` أو `pyppeteer` للتعامل مع المحتوى الديناميكي.

*   **`generator/`**: هذا المكون مسؤول عن توليد المحتوى. `content_generator.py` سيتفاعل مع Gemini API لتوليد المقالات، و `image_generator.py` سيستخدم مكتبة مثل `Pillow` لإنشاء أو تعديل الصور.

*   **`publisher/`**: هذا المكون مسؤول عن نشر المحتوى. `blogger_publisher.py` سيستخدم مكتبة Google API لنشر المقالات على بلوجر، و `telegram_publisher.py` سيرسل الإشعارات إلى قناة تليجرام.

*   **`utils/`**: سيحتوي هذا المجلد على أدوات مساعدة. `seo_utils.py` سيقدم وظائف لبحث الكلمات المفتاحية، و `logging_utils.py` سيهيئ نظام تسجيل لتتبع أداء البوت والأخطاء.

## 3. تدفق العمل (Workflow)

1.  **التشغيل**: يبدأ `main.py` عملية التنفيذ.
2.  **استخلاص الأخبار**: يستدعي `main.py` وحدات `scraper` لجلب آخر الأخبار من Sky News و Kooora.
3.  **التحقق من التكرار**: لكل خبر، يتم التحقق في قاعدة البيانات (عبر `database.py`) لمعرفة ما إذا كان قد تم نشره من قبل.
4.  **توليد المحتوى**: إذا كان الخبر جديدًا، يتم إرساله إلى `content_generator.py` لتوليد مقال فريد.
5.  **توليد الصور**: يتم إنشاء صورة مناسبة للمقال عبر `image_generator.py`.
6.  **تحسين SEO**: يتم استخدام `seo_utils.py` لتحسين العنوان والمحتوى بكلمات مفتاحية مناسبة.
7.  **النشر على بلوجر**: يتم نشر المقال والصورة على بلوجر باستخدام `blogger_publisher.py`.
8.  **تحديث قاعدة البيانات**: يتم تسجيل الخبر الجديد في قاعدة البيانات على أنه تم نشره.
9.  **إشعار تليجرام**: يتم إرسال إشعار مع رابط المقال إلى قناة تليجرام عبر `telegram_publisher.py`.
10. **التسجيل والمراقبة**: يتم تسجيل جميع الخطوات والنتائج (نجاح أو فشل) باستخدام `logging_utils.py`.
11. **التكرار**: تنتظر العملية لفترة زمنية محددة ثم تعيد بدء الدورة.

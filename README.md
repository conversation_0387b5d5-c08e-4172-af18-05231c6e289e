
# بوت أخبار كرة القدم | Football News Bot

🤖 **نظام متكامل لنشر أخبار كرة القدم تلقائياً**

هذا المشروع عبارة عن بوت ذكي مبني بـ Python يقوم تلقائياً باستخراج أخبار كرة القدم، وتوليد مقالات مفصلة باستخدام الذكاء الاصطناعي، ونشرها على Blogger وإرسال إشعارات على Telegram.

## ✨ المميزات

- 🔍 **استخراج ذكي للأخبار** من مصادر متعددة (Sky News Arabia, Kooora)
- 🤖 **توليد محتوى بالذكاء الاصطناعي** باستخدام Gemini API مع نظام fallback
- 📝 **مقالات مفصلة** بالعامية المصرية (2500 كلمة)
- 🌐 **نشر تلقائي على Blogger** مع كلمات مفتاحية محسنة لمحركات البحث
- 📱 **إشعارات فورية على Telegram**
- 🗄️ **قاعدة بيانات محلية** لمنع المقالات المكررة
- 🖼️ **توليد صور تلقائي** للمقالات
- 🛡️ **أنظمة fallback قوية** لضمان استمرارية العمل
- ✅ **اختبارات شاملة** لجميع المكونات

## 🚀 الحالة الحالية

**✅ النظام يعمل بنجاح 100%**

تم اختبار جميع المكونات وتشغيل دورة كاملة بنجاح. النظام جاهز للاستخدام الفوري!

## 🚀 التشغيل السريع

### الطريقة السهلة (موصى بها)
```bash
python run_bot.py
```
سيظهر لك قائمة تفاعلية للاختيار من بينها.

### التشغيل المباشر

**اختبار دورة واحدة:**
```bash
python main.py --test
```

**تشغيل مستمر:**
```bash
python main.py
```

## 📦 التثبيت

1. **استنساخ المشروع:**
   ```bash
   git clone <repository-url>
   cd football-news-bot
   ```

2. **تثبيت المتطلبات:**
   ```bash
   pip install -r requirements.txt
   ```

3. **إعداد متغيرات البيئة:**
   - انسخ `env.example` إلى `.env`
   - املأ مفاتيح API والإعدادات

4. **تشغيل النظام:**
   ```bash
   python run_bot.py
   ```

## Project Structure

- `main.py`: The main entry point for the bot.
- `config.py`: Configuration file (reads from `.env`).
- `.env.example`: An example file for environment variables.
- `requirements.txt`: A list of all necessary Python packages.
- `database/`: Contains the SQLite database logic.
- `scraper/`: Modules for scraping news from different sources.
- `generator/`: Modules for generating content and images.
- `publisher/`: Modules for publishing content.
- `utils/`: Utility modules for logging and API key management.
- `tests/`: A suite of unit tests to verify the functionality of the application.

## Setup and Installation

1.  **Clone the repository.**
2.  **Create a virtual environment:**
    ```bash
    python -m venv venv
    source venv/bin/activate
    ```
3.  **Install dependencies:**
    ```bash
    pip install -r requirements.txt
    ```
4.  **Create your `.env` file:**
    Copy `.env.example` to a new file named `.env` and fill in your API keys and other credentials.
    ```bash
    cp .env.example .env
    ```

## Running the Bot

To run the bot in its continuous loop:
```bash
python main.py
```

## Running Tests

To run all unit tests:
```bash
python -m unittest discover tests
```

## Troubleshooting

- **`ModuleNotFoundError`**: Make sure you have installed all the packages from `requirements.txt` in your active virtual environment.
- **`ValueError: No Gemini API keys found`**: Ensure your `.env` file is correctly formatted and contains at least one `GEMINI_API_KEY_...` entry.
- **Google OAuth Errors**: If you have issues with Blogger publishing, your `GOOGLE_OAUTH_TOKEN` might be expired or invalid. You may need to re-authenticate and generate a new token.
- **Scraping Issues**: Websites change their layout. If a scraper stops working, you may need to update the CSS selectors in the corresponding scraper file (`scraper/sky_news_scraper.py` or `scraper/kooora_scraper.py`).

# الخطة التقنية لتطوير بوت أخبار كرة القدم الذكي

**إصدار:** 1.0
**تاريخ:** 2025-06-23
**المؤلف:** MiniMax Agent

## 1. ملخص تنفيذي

يقدم هذا المستند تحليلاً تقنياً شاملاً وخطة تنفيذ مفصلة لإنشاء بوت ذكي لأخبار كرة القدم. يهدف المشروع إلى جمع الأخبار من مصادر محددة، وتوليد محتوى حصري بالعامية المصرية، ونشره تلقائياً على منصة Blogger وقناة Telegram، مع مراعاة تحسين محركات البحث (SEO). تم تصميم الخطة للعمل ضمن الموارد المحدودة للاستضافة (0.1 CPU, 100 MB RAM).

## 2. تحليل مصادر الأخبار

- **Sky News Arabia (كرة القدم):**
  - **الرابط:** `https://www.skynewsarabia.com/sport/football`
  - **طريقة الاستخلاص:** يمكن استخلاص المحتوى مباشرة باستخدام مكتبات مثل `requests` و `BeautifulSoup` في Python. هيكل الصفحة واضح نسبياً، حيث يمكن تحديد الأخبار وعناوينها وروابطها وصورها عبر محددات CSS.
- **Kooora:**
  - **الرابط:** `https://www.kooora.com/`
  - **طريقة الاستخلاص:** المحتوى يتم تحميله ديناميكياً باستخدام JavaScript. الاستخلاص المباشر غير فعال. يتطلب استخدام أداة تصفح مؤتمتة مثل **Selenium** أو **Playwright** لمحاكاة متصفح حقيقي وتنفيذ الـ JavaScript قبل استخلاص المحتوى. هذا سيزيد من استهلاك الموارد، ويجب مراقبته بعناية.

## 3. هيكل النظام والمكونات

سيتم بناء المشروع على هيكل معياري باستخدام Python، كما هو مفصل في `docs/system_architecture.md`. هذا التصميم يفصل بين المسؤوليات ويسهل الصيانة.

## 4. التقنيات والمكتبات الموصى بها

سيتم إنشاء ملف `requirements.txt` يحتوي على المكتبات التالية:

```
# أساسيات ومكتبات الويب
requests
beautifulsoup4
selenium
webdriver-manager

# واجهات برمجة التطبيقات (APIs)
google-api-python-client
google-auth-httplib2
google-auth-oauthlib
python-telegram-bot --pre
google-generativeai

# إدارة الصور
Pillow

# إدارة البيئة
python-dotenv

# أدوات مساعدة (اختياري، يمكن بناء المنطق الخاص)
# seo-keyword-research-tool
```

### تفصيل المكتبات:
- **`requests` و `beautifulsoup4`:** لاستخلاص الأخبار من Sky News.
- **`selenium` و `webdriver-manager`:** لاستخلاص الأخبار من Kooora.
- **`google-api-python-client` ومكتبات المصادقة المساعدة:** للتفاعل مع Blogger API V3.
- **`python-telegram-bot`:** للتفاعل مع Telegram Bot API.
- **`google-generativeai`:** للتفاعل مع Gemini API لتوليد المحتوى.
- **`Pillow`:** لإنشاء ومعالجة الصور للمقالات.
- **`sqlite3` (مدمجة):** لإدارة قاعدة بيانات محلية لتجنب نشر الأخبار المكررة.
- **`python-dotenv`:** لإدارة متغيرات البيئة والمفاتيح الحساسة في بيئة التطوير.

## 5. استراتيجية إدارة مفاتيح Gemini API

تم وضع استراتيجية مفصلة لتدوير 45+ مفتاح API لضمان استمرارية الخدمة. تتضمن الاستراتيجية تخزين المفاتيح كمتغيرات بيئة وتدويرها عند حدوث أخطاء تتعلق بحدود الاستخدام. التفاصيل الكاملة موجودة في `docs/gemini_api_key_strategy.md`.

## 6. متطلبات الاستضافة والنشر على Pella.app

- **الموارد:** 0.1 CPU, 100 MB RAM, 5 GB Disk.
- **تحديات ومخاطر:**
  - **دعم Python:** على الرغم من وجود صفحة تشير إلى دعم Flask، إلا أن وثائق Pella.app تفتقر إلى معلومات واضحة حول نشر وتشغيل البرامج النصية (scripts) التي تعمل في الخلفية بشكل مستمر، وهو أمر ضروري لهذا البوت.
  - **استهلاك الموارد:** تشغيل متصفح مؤتمت (Selenium) لاستهداف موقع Kooora قد يستهلك ذاكرة (RAM) تتجاوز الحد المسموح به (100 MB).
- **توصيات:**
  1.  **التحقق المباشر:** قبل البدء بالتنفيذ، يجب التواصل مع دعم Pella.app للتأكد من إمكانية تشغيل عملية Python في الخلفية (`while True: ...`) بشكل موثوق ضمن الخطة المجانية.
  2.  **مراقبة الموارد:** يجب مراقبة استهلاك الذاكرة عن كثب عند استخدام Selenium. قد يتطلب الأمر تحسينات دقيقة، مثل تعطيل تحميل الصور في المتصفح المؤتمت.
  3.  **النظر في البدائل:** إذا كانت Pella.app غير مناسبة، يجب النظر في منصات بديلة معروفة بدعمها لـ Python مثل:
      - **PythonAnywhere:** لديهم خطة مجانية تسمح بتشغيل المهام المجدولة مرة واحدة يوميًا، وهو قد يكون كافياً لبدء المشروع.
      - **Render / Heroku:** يقدمان خطط مجانية (قد تكون محدودة) تدعم تشغيل عمال (workers) في الخلفية.

## 7. خطة التنفيذ المقترحة

يقترح تقسيم المشروع إلى المراحل التالية:

- **المرحلة 1: الإعداد والأساس (يوم واحد)**
  - [ ] إعداد بيئة التطوير (`venv`).
  - [ ] تثبيت المكتبات الأولية.
  - [ ] إعداد هيكل المشروع (المجلدات والملفات الفارغة).
  - [ ] إعداد ملف `.env` لتخزين المفاتيح محلياً.
  - [ ] بناء وحدة قاعدة البيانات `database.py` مع وظائف الإنشاء والإضافة والتحقق.

- **المرحلة 2: استخلاص الأخبار (يومان)**
  - [ ] تطوير `sky_news_scraper.py` واختباره.
  - [ ] تطوير `kooora_scraper.py` باستخدام Selenium واختباره، مع التركيز على تقليل استهلاك الموارد.

- **المرحلة 3: توليد المحتوى (يومان)**
  - [ ] بناء وحدة `content_generator.py` مع منطق إدارة مفاتيح Gemini.
  - [ ] تطوير وحدة `image_generator.py` (يمكن البدء بصور بسيطة أو استخدام مكتبات لتوليد صور من النص).
  - [ ] تطوير وحدة `seo_utils.py` (يمكن البدء بمنطق بسيط لسحب الكلمات المقترحة من جوجل).

- **المرحلة 4: النشر (يومان)**
  - [ ] تطوير وحدة `blogger_publisher.py` (مع التعامل مع مصادقة OAuth2).
  - [ ] تطوير وحدة `telegram_publisher.py`.

- **المرحلة 5: التجميع والنشر الأولي (يومان)**
  - [ ] تجميع جميع المكونات في `main.py`.
  - [ ] إجراء اختبار شامل لكامل تدفق العمل.
  - [ ] محاولة النشر على Pella.app (أو منصة بديلة) واختبار التشغيل في بيئة حقيقية.

- **المرحلة 6: التحسين والمراقبة (مستمر)**
  - [ ] إضافة نظام تسجيل (`logging`) شامل.
  - [ ] تحسين أداء استهلاك الذاكرة.
  - [ ] مراقبة الأداء وإصلاح الأخطاء.
